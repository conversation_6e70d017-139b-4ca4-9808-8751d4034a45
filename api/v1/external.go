/**
* <AUTHOR>
* @date 2023-03-10 18:10
* @description
 */

package v1

import "github.com/gofiber/fiber/v2"

// External 对外提供服务的路由
func (r *Router) External(fa *fiber.App) {
	api := fa.Group("/api")
	v1 := api.Group("/v1")
	externalRouter := v1.Group("/external")

	// 安全部推送
	externalRouter.Post("/alert/push", r.AuthApiKey.AuthSocGroupApiKey(), r.Gen.SaCtx(), r.NdsController.Push)
	externalRouter.Get("/wofang/drain", r.Auth<PERSON>pi<PERSON>.AuthSocGroupApiKey(), r.Gen.SaCtx(), r.WoFang<PERSON>pi2.Query)

	// soc获取数据
	// 分光告警数据
	externalRouter.Get("/spectrumalert",
		r.JWT.AuthJWT(),
		r.Casbinx.Auth<PERSON>bin(r.Enf),
		r.<PERSON><PERSON><PERSON>.LogUserOperation(),
		// r.Gen.Auth<PERSON>tx(),
		r.<PERSON>.<PERSON>t<PERSON>(), // 使用sacontext 获取所有租户数据
		r.SpectrumAlertController.Query)
}
