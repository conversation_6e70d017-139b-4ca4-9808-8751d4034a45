package test

import (
	"meta/app/ent"
	"meta/app/ent/strategy"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var strategyApi = baseApi + "/strategy"
var remark31 = "JywhsQutAy"
var testStrategy = &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark31, Name: "ypXURrSORs", Type: 383, Bps: 332, Pps: 320, BpsCount: 340, PpsCount: 351, IspCode: 377}
var strategyIDs = []int{}

// Create Strategy test case
// 创建
func TestCreateStrategy(t *testing.T) {
	successExpectedResult.ResponseData = testStrategy.Name
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "POST", BodyData: testStrategy, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	strategyIDs = append(strategyIDs, getDataMapId(result))
}

// CreateBulk Strategy test case
// 批量创建
func TestCreateBulkStrategy(t *testing.T) {
	remark1 := "nsMLtaArSU"
	bulkData1 := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, Name: "lxLaifACjd", Type: 320, Bps: 343, Pps: 361, BpsCount: 377, PpsCount: 34, IspCode: 31}
	remark2 := "yzPHFMociW"
	bulkData2 := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, Name: "BBnVqJbyCH", Type: 339, Bps: 316, Pps: 388, BpsCount: 331, PpsCount: 384, IspCode: 348}
	remark3 := "cVaqQtQASW"
	bulkData3 := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, Name: "viLsDzcYWI", Type: 339, Bps: 395, Pps: 37, BpsCount: 327, PpsCount: 350, IspCode: 338}
	remark4 := "hZANnmDXwU"
	bulkData4 := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, Name: "BWYLctFPhj", Type: 366, Bps: 345, Pps: 334, BpsCount: 363, PpsCount: 314, IspCode: 330}
	remark5 := "yxRLSQwcFj"
	bulkData5 := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, Name: "KVwyDMhhmq", Type: 379, Bps: 373, Pps: 350, BpsCount: 364, PpsCount: 321, IspCode: 338}
	bulkDatas := [...]ent.Strategy{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.Name
	testCase := &CaseRule{Api: strategyApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		strategyIDs = append(strategyIDs, getDataMapId(v))
	}
}

// Query Strategy test case
// 根据指定字段、时间范围查询或搜索
func TestQueryStrategy(t *testing.T) {
	successExpectedResult.ResponseData = testStrategy.Name
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID Strategy test case
// 根据 ID 查询
func TestQueryByIDStrategy(t *testing.T) {
	successExpectedResult.ResponseData = testStrategy.Name
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "GET", UrlData: strconv.Itoa(strategyIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID Strategy not exist test case
// 根据 ID 查询
func TestQueryByIDStrategyNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testStrategy.Name
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query Strategy by Name test case
// 根据指定字段、时间范围查询或搜索
func TestQueryStrategyByName(t *testing.T) {
	successExpectedResult.ResponseData = testStrategy.Name
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "GET", UrlData: strategy.FieldName + "=" + testStrategy.Name, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch Strategy search by Name test case
// 分页搜索
func TestQuerySearchStrategyName(t *testing.T) {
	successExpectedResult.ResponseData = testStrategy.Name
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "GET", UrlData: "search=" + testStrategy.Name, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID Strategy test case
// 根据 ID 修改
func TestUpdateByIDStrategy(t *testing.T) {
	remark31 := "fBqjfQHezY"
	updateData := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark31, Name: "nEVgsfdrID", Type: 31, Bps: 334, Pps: 316, BpsCount: 368, PpsCount: 388, IspCode: 385}
	successExpectedResult.ResponseData = updateData.Name
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "PUT", UrlData: strconv.Itoa(strategyIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID Strategy test case
// 根据 ID 删除
func TestDeleteByIDStrategy(t *testing.T) {
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(strategyIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID Strategy not exist test case
// 根据 ID 删除
func TestDeleteByIDStrategyNoExist(t *testing.T) {
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk Strategy test case
// 根据 IDs 批量删除
func TestDeleteBulkStrategy(t *testing.T) {
	testCase := &CaseRule{Api: strategyApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: strategyIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
