package test

import (
	"meta/app/ent"
	"meta/app/ent/alicloudoriginattackdata"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var aliCloudOriginAttackDataApi = baseApi + "/alicloudoriginattackdata"
var remark101 = "nTelKJhUJs"
var testAliCloudOriginAttackData = &ent.AliCloudOriginAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark101, Project: "lkHEtvDaId", CloudType: "XUGibmpnWl", IP: "hcuKhjVAHs", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Duration: 319, Pps: 372, Mbps: 36, Status: "JTwLFXoKSw"}
var aliCloudOriginAttackDataIDs = []int{}

// Create AliCloudOriginAttackData test case
// 创建
func TestCreateAliCloudOriginAttackData(t *testing.T) {
	successExpectedResult.ResponseData = testAliCloudOriginAttackData.Project
	testCase := &CaseRule{Api: aliCloudOriginAttackDataApi, HttpMethod: "POST", BodyData: testAliCloudOriginAttackData, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	aliCloudOriginAttackDataIDs = append(aliCloudOriginAttackDataIDs, getDataMapId(result))
}

// CreateBulk AliCloudOriginAttackData test case
// 批量创建
func TestCreateBulkAliCloudOriginAttackData(t *testing.T) {
	remark1 := "hJhimcxbVh"
	bulkData1 := &ent.AliCloudOriginAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, Project: "wKTymjyvAT", CloudType: "ntShvovxOB", IP: "OYsPXJlyjj", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Duration: 377, Pps: 345, Mbps: 312, Status: "aFxFWtcXnP"}
	remark2 := "VLEwyiubRW"
	bulkData2 := &ent.AliCloudOriginAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, Project: "TAwLTDVUry", CloudType: "uONHtEzyDa", IP: "WNlTcqLaEw", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Duration: 376, Pps: 381, Mbps: 345, Status: "PpolpbSrlE"}
	remark3 := "ErkmwhHVat"
	bulkData3 := &ent.AliCloudOriginAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, Project: "NwjjhYrInd", CloudType: "efLpocERBY", IP: "EUhrtRUdLT", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Duration: 353, Pps: 397, Mbps: 349, Status: "gizXQDrBKQ"}
	remark4 := "YZiDDNVmbn"
	bulkData4 := &ent.AliCloudOriginAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, Project: "xkoHijzzoO", CloudType: "xMofwMQzSY", IP: "NJaVaobGvY", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Duration: 356, Pps: 376, Mbps: 35, Status: "LfWVYxQBxa"}
	remark5 := "OjuejarGUr"
	bulkData5 := &ent.AliCloudOriginAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, Project: "MlZCBxdnFM", CloudType: "iLusWIdrRN", IP: "yXhQFhJKKV", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Duration: 327, Pps: 314, Mbps: 399, Status: "StQVbDvhWE"}
	bulkDatas := [...]ent.AliCloudOriginAttackData{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.Project
	testCase := &CaseRule{Api: aliCloudOriginAttackDataApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		aliCloudOriginAttackDataIDs = append(aliCloudOriginAttackDataIDs, getDataMapId(v))
	}
}

// Query AliCloudOriginAttackData test case
// 根据指定字段、时间范围查询或搜索
func TestQueryAliCloudOriginAttackData(t *testing.T) {
	successExpectedResult.ResponseData = testAliCloudOriginAttackData.Project
	testCase := &CaseRule{Api: aliCloudOriginAttackDataApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID AliCloudOriginAttackData test case
// 根据 ID 查询
func TestQueryByIDAliCloudOriginAttackData(t *testing.T) {
	successExpectedResult.ResponseData = testAliCloudOriginAttackData.Project
	testCase := &CaseRule{Api: aliCloudOriginAttackDataApi, HttpMethod: "GET", UrlData: strconv.Itoa(aliCloudOriginAttackDataIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID AliCloudOriginAttackData not exist test case
// 根据 ID 查询
func TestQueryByIDAliCloudOriginAttackDataNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testAliCloudOriginAttackData.Project
	testCase := &CaseRule{Api: aliCloudOriginAttackDataApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query AliCloudOriginAttackData by Project test case
// 根据指定字段、时间范围查询或搜索
func TestQueryAliCloudOriginAttackDataByProject(t *testing.T) {
	successExpectedResult.ResponseData = testAliCloudOriginAttackData.Project
	testCase := &CaseRule{Api: aliCloudOriginAttackDataApi, HttpMethod: "GET", UrlData: alicloudoriginattackdata.FieldProject + "=" + testAliCloudOriginAttackData.Project, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch AliCloudOriginAttackData search by Project test case
// 分页搜索
func TestQuerySearchAliCloudOriginAttackDataProject(t *testing.T) {
	successExpectedResult.ResponseData = testAliCloudOriginAttackData.Project
	testCase := &CaseRule{Api: aliCloudOriginAttackDataApi, HttpMethod: "GET", UrlData: "search=" + testAliCloudOriginAttackData.Project, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID AliCloudOriginAttackData test case
// 根据 ID 修改
func TestUpdateByIDAliCloudOriginAttackData(t *testing.T) {
	remark101 := "ufLCopOUvl"
	updateData := &ent.AliCloudOriginAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark101, Project: "VKwqYnHYan", CloudType: "eBjYfunRTr", IP: "ETYIvtmKhc", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Duration: 342, Pps: 351, Mbps: 310, Status: "IvinxwpxFx"}
	successExpectedResult.ResponseData = updateData.Project
	testCase := &CaseRule{Api: aliCloudOriginAttackDataApi, HttpMethod: "PUT", UrlData: strconv.Itoa(aliCloudOriginAttackDataIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID AliCloudOriginAttackData test case
// 根据 ID 删除
func TestDeleteByIDAliCloudOriginAttackData(t *testing.T) {
	testCase := &CaseRule{Api: aliCloudOriginAttackDataApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(aliCloudOriginAttackDataIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID AliCloudOriginAttackData not exist test case
// 根据 ID 删除
func TestDeleteByIDAliCloudOriginAttackDataNoExist(t *testing.T) {
	testCase := &CaseRule{Api: aliCloudOriginAttackDataApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk AliCloudOriginAttackData test case
// 根据 IDs 批量删除
func TestDeleteBulkAliCloudOriginAttackData(t *testing.T) {
	testCase := &CaseRule{Api: aliCloudOriginAttackDataApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: aliCloudOriginAttackDataIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
