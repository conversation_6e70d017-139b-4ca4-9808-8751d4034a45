package test

import (
	"meta/app/ent"
	"meta/app/ent/alicloudoriginattackdata"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var aliCloudOriginAttackDataApi = baseApi + "/alicloudoriginattackdata"
var remark23 = "KFSiFueAPx"
var testAliCloudOriginAttackData = &ent.AliCloudOriginAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark23, EventType: "ViRoPZworp", Project: "xkBShZgDAt", IP: "eOPYiqpQUG", Region: "rsNArkGPtP", Action: "lKfVRpVJUz", Time: time.Now().AddDate(0, 0, -1), Pps: 310, Bps: 324, DdosType: "RQCMmaqGWa"}
var aliCloudOriginAttackDataIDs = []int{}

// Create AliCloudOriginAttackData test case
// 创建
func TestCreateAliCloudOriginAttackData(t *testing.T) {
	successExpectedResult.ResponseData = testAliCloudOriginAttackData.EventType
	testCase := &CaseRule{Api: aliCloudOriginAttackDataApi, HttpMethod: "POST", BodyData: testAliCloudOriginAttackData, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	aliCloudOriginAttackDataIDs = append(aliCloudOriginAttackDataIDs, getDataMapId(result))
}

// CreateBulk AliCloudOriginAttackData test case
// 批量创建
func TestCreateBulkAliCloudOriginAttackData(t *testing.T) {
	remark1 := "bcGhyHfawp"
	bulkData1 := &ent.AliCloudOriginAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, EventType: "mdEWYkOSAm", Project: "rGQBCUWfdy", IP: "vlJiETQnFp", Region: "AFoNZlAWCE", Action: "PoCjNTAVXT", Time: time.Now().AddDate(0, 0, -1), Pps: 33, Bps: 320, DdosType: "bjCnAHWaAP"}
	remark2 := "BWBpcFbcdG"
	bulkData2 := &ent.AliCloudOriginAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, EventType: "cdxpphMruj", Project: "vufYKsxYcb", IP: "PkIXEJpCCZ", Region: "eFrzEkeiUt", Action: "qsKLJwOLFl", Time: time.Now().AddDate(0, 0, -1), Pps: 351, Bps: 347, DdosType: "JAipPPKaUd"}
	remark3 := "WgQQOBLaHH"
	bulkData3 := &ent.AliCloudOriginAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, EventType: "DvKyzFIeLZ", Project: "xvUgLqxBDP", IP: "aNauKErICJ", Region: "YKIsCzPsTS", Action: "ChzNRFAxCC", Time: time.Now().AddDate(0, 0, -1), Pps: 369, Bps: 330, DdosType: "nnMGSAYuIz"}
	remark4 := "BTvHNlZFdH"
	bulkData4 := &ent.AliCloudOriginAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, EventType: "bVBZWDXrbC", Project: "euAioyiBHF", IP: "VTmlWcduVY", Region: "zYNWngNedV", Action: "DdUxjxnqFY", Time: time.Now().AddDate(0, 0, -1), Pps: 351, Bps: 358, DdosType: "zRgIaUNRbV"}
	remark5 := "btoGrIJvgO"
	bulkData5 := &ent.AliCloudOriginAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, EventType: "LuVwYjbLDc", Project: "mXAHNFJoOh", IP: "rolHSDHuSb", Region: "UfbwNBMetL", Action: "NuYBhvUgYM", Time: time.Now().AddDate(0, 0, -1), Pps: 366, Bps: 370, DdosType: "HxHdxfMMgc"}
	bulkDatas := [...]ent.AliCloudOriginAttackData{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.EventType
	testCase := &CaseRule{Api: aliCloudOriginAttackDataApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		aliCloudOriginAttackDataIDs = append(aliCloudOriginAttackDataIDs, getDataMapId(v))
	}
}

// Query AliCloudOriginAttackData test case
// 根据指定字段、时间范围查询或搜索
func TestQueryAliCloudOriginAttackData(t *testing.T) {
	successExpectedResult.ResponseData = testAliCloudOriginAttackData.EventType
	testCase := &CaseRule{Api: aliCloudOriginAttackDataApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID AliCloudOriginAttackData test case
// 根据 ID 查询
func TestQueryByIDAliCloudOriginAttackData(t *testing.T) {
	successExpectedResult.ResponseData = testAliCloudOriginAttackData.EventType
	testCase := &CaseRule{Api: aliCloudOriginAttackDataApi, HttpMethod: "GET", UrlData: strconv.Itoa(aliCloudOriginAttackDataIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID AliCloudOriginAttackData not exist test case
// 根据 ID 查询
func TestQueryByIDAliCloudOriginAttackDataNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testAliCloudOriginAttackData.EventType
	testCase := &CaseRule{Api: aliCloudOriginAttackDataApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query AliCloudOriginAttackData by EventType test case
// 根据指定字段、时间范围查询或搜索
func TestQueryAliCloudOriginAttackDataByEventType(t *testing.T) {
	successExpectedResult.ResponseData = testAliCloudOriginAttackData.EventType
	testCase := &CaseRule{Api: aliCloudOriginAttackDataApi, HttpMethod: "GET", UrlData: alicloudoriginattackdata.FieldEventType + "=" + testAliCloudOriginAttackData.EventType, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch AliCloudOriginAttackData search by EventType test case
// 分页搜索
func TestQuerySearchAliCloudOriginAttackDataEventType(t *testing.T) {
	successExpectedResult.ResponseData = testAliCloudOriginAttackData.EventType
	testCase := &CaseRule{Api: aliCloudOriginAttackDataApi, HttpMethod: "GET", UrlData: "search=" + testAliCloudOriginAttackData.EventType, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID AliCloudOriginAttackData test case
// 根据 ID 修改
func TestUpdateByIDAliCloudOriginAttackData(t *testing.T) {
	remark23 := "BBYblDzvMf"
	updateData := &ent.AliCloudOriginAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark23, EventType: "WouoxmbiHv", Project: "TAOqXLgyuR", IP: "LZhHdMgdxw", Region: "KSQTVWlnfO", Action: "hWgaScGEBr", Time: time.Now().AddDate(0, 0, -1), Pps: 318, Bps: 318, DdosType: "wWRpnZXjLm"}
	successExpectedResult.ResponseData = updateData.EventType
	testCase := &CaseRule{Api: aliCloudOriginAttackDataApi, HttpMethod: "PUT", UrlData: strconv.Itoa(aliCloudOriginAttackDataIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID AliCloudOriginAttackData test case
// 根据 ID 删除
func TestDeleteByIDAliCloudOriginAttackData(t *testing.T) {
	testCase := &CaseRule{Api: aliCloudOriginAttackDataApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(aliCloudOriginAttackDataIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID AliCloudOriginAttackData not exist test case
// 根据 ID 删除
func TestDeleteByIDAliCloudOriginAttackDataNoExist(t *testing.T) {
	testCase := &CaseRule{Api: aliCloudOriginAttackDataApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk AliCloudOriginAttackData test case
// 根据 IDs 批量删除
func TestDeleteBulkAliCloudOriginAttackData(t *testing.T) {
	testCase := &CaseRule{Api: aliCloudOriginAttackDataApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: aliCloudOriginAttackDataIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
