package test

import (
	"meta/app/ent"
	"meta/app/ent/cloudalert"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var cloudAlertApi = baseApi + "/cloudalert"
var remarkCloudAlert = "ZXjRxfEGkK"
var testCloudAlert = &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remarkCloudAlert, SrcIP: "BOvCFqCQVN", SrcPort: 330, DstIP: "iNFgZkFAfe", DstPort: 329, DefenceMode: 341, FlowMode: 378, TCPAckNum: "eXEQKnAUoo", TCPSeqNum: "FxmXrCjHVq", Protocol: 369, DefenceLevel: 123, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
var cloudAlertIDs = []int{}

// Create CloudAlert test case
// 创建
func TestCreateCloudAlert(t *testing.T) {
	successExpectedResult.ResponseData = testCloudAlert.SrcIP
	testCase := &CaseRule{Api: cloudAlertApi, HttpMethod: "POST", BodyData: testCloudAlert, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	cloudAlertIDs = append(cloudAlertIDs, getDataMapId(result))
}

// CreateBulk CloudAlert test case
// 批量创建
func TestCreateBulkCloudAlert(t *testing.T) {
	remark1 := "dBBwsEevAn"
	bulkData1 := &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, SrcIP: "yXnJCnDKHw", SrcPort: 394, DstIP: "iwyIpCUTGX", DstPort: 366, DefenceMode: 32, FlowMode: 312, TCPAckNum: "bvyizxhbhD", TCPSeqNum: "cAvLQAkioc", Protocol: 397, DefenceLevel: 456, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
	remark2 := "JcYvLVtxRd"
	bulkData2 := &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, SrcIP: "EumpjStdkV", SrcPort: 38, DstIP: "hYRAsstOpw", DstPort: 384, DefenceMode: 396, FlowMode: 323, TCPAckNum: "ZaFCsTvPtn", TCPSeqNum: "TAFokAKBVR", Protocol: 392, DefenceLevel: 789, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
	remark3 := "RxnKIEIOAP"
	bulkData3 := &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, SrcIP: "kcoGsjioxI", SrcPort: 349, DstIP: "JtokybQQnF", DstPort: 328, DefenceMode: 383, FlowMode: 322, TCPAckNum: "jNpQciYtmH", TCPSeqNum: "eOpfcckrLv", Protocol: 327, DefenceLevel: 234, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
	remark4 := "DKHaTWcfcO"
	bulkData4 := &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, SrcIP: "RqISxsiYPd", SrcPort: 347, DstIP: "DXNCvyJrNH", DstPort: 322, DefenceMode: 330, FlowMode: 337, TCPAckNum: "GNLAhinNYk", TCPSeqNum: "RprDjHuKfo", Protocol: 322, DefenceLevel: 567, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
	remark5 := "tMQlRmQkQb"
	bulkData5 := &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, SrcIP: "zooWmeRCai", SrcPort: 327, DstIP: "aEypEUEKyp", DstPort: 375, DefenceMode: 330, FlowMode: 369, TCPAckNum: "FZuhXznlWv", TCPSeqNum: "ZAIRSYFjLy", Protocol: 313, DefenceLevel: 890, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
	bulkDatas := [...]ent.CloudAlert{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.SrcIP
	testCase := &CaseRule{Api: cloudAlertApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		cloudAlertIDs = append(cloudAlertIDs, getDataMapId(v))
	}
}

// Query CloudAlert test case
// 根据指定字段、时间范围查询或搜索
func TestQueryCloudAlert(t *testing.T) {
	successExpectedResult.ResponseData = testCloudAlert.SrcIP
	testCase := &CaseRule{Api: cloudAlertApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID CloudAlert test case
// 根据 ID 查询
func TestQueryByIDCloudAlert(t *testing.T) {
	successExpectedResult.ResponseData = testCloudAlert.SrcIP
	testCase := &CaseRule{Api: cloudAlertApi, HttpMethod: "GET", UrlData: strconv.Itoa(cloudAlertIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID CloudAlert not exist test case
// 根据 ID 查询
func TestQueryByIDCloudAlertNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testCloudAlert.SrcIP
	testCase := &CaseRule{Api: cloudAlertApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query CloudAlert by SrcIP test case
// 根据指定字段、时间范围查询或搜索
func TestQueryCloudAlertBySrcIP(t *testing.T) {
	successExpectedResult.ResponseData = testCloudAlert.SrcIP
	testCase := &CaseRule{Api: cloudAlertApi, HttpMethod: "GET", UrlData: cloudalert.FieldSrcIP + "=" + testCloudAlert.SrcIP, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch CloudAlert search by SrcIP test case
// 分页搜索
func TestQuerySearchCloudAlertSrcIP(t *testing.T) {
	successExpectedResult.ResponseData = testCloudAlert.SrcIP
	testCase := &CaseRule{Api: cloudAlertApi, HttpMethod: "GET", UrlData: "search=" + testCloudAlert.SrcIP, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID CloudAlert test case
// 根据 ID 修改
func TestUpdateByIDCloudAlert(t *testing.T) {
	remarkUpdate := "sTzJieMCeH"
	updateData := &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remarkUpdate, SrcIP: "xmfulozyZV", SrcPort: 333, DstIP: "ACtcCDWpoC", DstPort: 348, DefenceMode: 35, FlowMode: 344, TCPAckNum: "aEkQWAFIIa", TCPSeqNum: "BTtGMUyjyg", Protocol: 381, DefenceLevel: 999, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
	successExpectedResult.ResponseData = updateData.SrcIP
	testCase := &CaseRule{Api: cloudAlertApi, HttpMethod: "PUT", UrlData: strconv.Itoa(cloudAlertIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID CloudAlert test case
// 根据 ID 删除
func TestDeleteByIDCloudAlert(t *testing.T) {
	testCase := &CaseRule{Api: cloudAlertApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(cloudAlertIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID CloudAlert not exist test case
// 根据 ID 删除
func TestDeleteByIDCloudAlertNoExist(t *testing.T) {
	testCase := &CaseRule{Api: cloudAlertApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk CloudAlert test case
// 根据 IDs 批量删除
func TestDeleteBulkCloudAlert(t *testing.T) {
	testCase := &CaseRule{Api: cloudAlertApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: cloudAlertIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
