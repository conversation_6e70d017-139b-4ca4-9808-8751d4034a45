package test

import (
	"meta/app/ent"
	"meta/app/ent/matrixspectrumdata"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var matrixSpectrumDataApi = baseApi + "/matrixspectrumdata"
var testMatrixSpectrumData = &ent.MatrixSpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Region: "sEYxZrVywd", NetType: "QRUQETwHrt", Isp: "CMidpfQQRC", Bps: 36, Time: time.Now().AddDate(0, 0, -1)}
var matrixSpectrumDataIDs = []int{}

// Create MatrixSpectrumData test case
// 创建
func TestCreateMatrixSpectrumData(t *testing.T) {
	successExpectedResult.ResponseData = testMatrixSpectrumData.Region
	testCase := &CaseRule{Api: matrixSpectrumDataApi, HttpMethod: "POST", BodyData: testMatrixSpectrumData, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	matrixSpectrumDataIDs = append(matrixSpectrumDataIDs, getDataMapId(result))
}

// CreateBulk MatrixSpectrumData test case
// 批量创建
func TestCreateBulkMatrixSpectrumData(t *testing.T) {
	bulkData1 := &ent.MatrixSpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Region: "cpHargGrKH", NetType: "ltfLvClExs", Isp: "bdQKspOdJJ", Bps: 342, Time: time.Now().AddDate(0, 0, -1)}
	bulkData2 := &ent.MatrixSpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Region: "grGvRNfvxD", NetType: "wwCAIFXkAP", Isp: "RCriodjHSl", Bps: 386, Time: time.Now().AddDate(0, 0, -1)}
	bulkData3 := &ent.MatrixSpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Region: "hpIiSezTHK", NetType: "qeiFzMiNzr", Isp: "EHTtdqCwIp", Bps: 370, Time: time.Now().AddDate(0, 0, -1)}
	bulkData4 := &ent.MatrixSpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Region: "xWJzELTJAW", NetType: "YdOPPtJYDe", Isp: "KQGUdjDuZe", Bps: 357, Time: time.Now().AddDate(0, 0, -1)}
	bulkData5 := &ent.MatrixSpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Region: "KiNvNoftKL", NetType: "NFqhJPrLAV", Isp: "ZfWfshKeHa", Bps: 374, Time: time.Now().AddDate(0, 0, -1)}
	bulkDatas := [...]ent.MatrixSpectrumData{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.Region
	testCase := &CaseRule{Api: matrixSpectrumDataApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		matrixSpectrumDataIDs = append(matrixSpectrumDataIDs, getDataMapId(v))
	}
}

// Query MatrixSpectrumData test case
// 根据指定字段、时间范围查询或搜索
func TestQueryMatrixSpectrumData(t *testing.T) {
	successExpectedResult.ResponseData = testMatrixSpectrumData.Region
	testCase := &CaseRule{Api: matrixSpectrumDataApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID MatrixSpectrumData test case
// 根据 ID 查询
func TestQueryByIDMatrixSpectrumData(t *testing.T) {
	successExpectedResult.ResponseData = testMatrixSpectrumData.Region
	testCase := &CaseRule{Api: matrixSpectrumDataApi, HttpMethod: "GET", UrlData: strconv.Itoa(matrixSpectrumDataIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID MatrixSpectrumData not exist test case
// 根据 ID 查询
func TestQueryByIDMatrixSpectrumDataNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testMatrixSpectrumData.Region
	testCase := &CaseRule{Api: matrixSpectrumDataApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query MatrixSpectrumData by Region test case
// 根据指定字段、时间范围查询或搜索
func TestQueryMatrixSpectrumDataByRegion(t *testing.T) {
	successExpectedResult.ResponseData = testMatrixSpectrumData.Region
	testCase := &CaseRule{Api: matrixSpectrumDataApi, HttpMethod: "GET", UrlData: matrixspectrumdata.FieldRegion + "=" + testMatrixSpectrumData.Region, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch MatrixSpectrumData search by Region test case
// 分页搜索
func TestQuerySearchMatrixSpectrumDataRegion(t *testing.T) {
	successExpectedResult.ResponseData = testMatrixSpectrumData.Region
	testCase := &CaseRule{Api: matrixSpectrumDataApi, HttpMethod: "GET", UrlData: "search=" + testMatrixSpectrumData.Region, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID MatrixSpectrumData test case
// 根据 ID 修改
func TestUpdateByIDMatrixSpectrumData(t *testing.T) {
	updateData := &ent.MatrixSpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Region: "hAHKpUNPBc", NetType: "SywZFHfmkd", Isp: "CjPCkwcFEg", Bps: 388, Time: time.Now().AddDate(0, 0, -1)}
	successExpectedResult.ResponseData = updateData.Region
	testCase := &CaseRule{Api: matrixSpectrumDataApi, HttpMethod: "PUT", UrlData: strconv.Itoa(matrixSpectrumDataIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID MatrixSpectrumData test case
// 根据 ID 删除
func TestDeleteByIDMatrixSpectrumData(t *testing.T) {
	testCase := &CaseRule{Api: matrixSpectrumDataApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(matrixSpectrumDataIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID MatrixSpectrumData not exist test case
// 根据 ID 删除
func TestDeleteByIDMatrixSpectrumDataNoExist(t *testing.T) {
	testCase := &CaseRule{Api: matrixSpectrumDataApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk MatrixSpectrumData test case
// 根据 IDs 批量删除
func TestDeleteBulkMatrixSpectrumData(t *testing.T) {
	testCase := &CaseRule{Api: matrixSpectrumDataApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: matrixSpectrumDataIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
