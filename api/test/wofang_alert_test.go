package test

import (
	"meta/app/ent"
	"meta/app/ent/wofangalert"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var wofangAlertApi = baseApi + "/wofangalert"
var remark13 = "TELleGbqTI"
var testWofangAlert = &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark13, AttackStatus: 330, DeviceIP: "Wnedwxdsto", ZoneIP: "MdYAvstNxG", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 342, MaxInBps: 32}
var wofangAlertIDs = []int{}

// Create WofangAlert test case
// 创建
func TestCreateWofangAlert(t *testing.T) {
	successExpectedResult.ResponseData = testWofangAlert.DeviceIP
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "POST", BodyData: testWofangAlert, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	wofangAlertIDs = append(wofangAlertIDs, getDataMapId(result))
}

// CreateBulk WofangAlert test case
// 批量创建
func TestCreateBulkWofangAlert(t *testing.T) {
	remark1 := "hdWonRzqer"
	bulkData1 := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, AttackStatus: 350, DeviceIP: "dkogBjvsvD", ZoneIP: "YoebhLphbg", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 377, MaxInBps: 392}
	remark2 := "iquBVHhBbS"
	bulkData2 := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, AttackStatus: 366, DeviceIP: "rXFHzyOcRp", ZoneIP: "FVEEhWNPvu", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 388, MaxInBps: 320}
	remark3 := "RsZlEoHkVh"
	bulkData3 := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, AttackStatus: 344, DeviceIP: "OQlsAsdCsg", ZoneIP: "VsFPcnXfcz", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 395, MaxInBps: 348}
	remark4 := "kXOgluBBjZ"
	bulkData4 := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, AttackStatus: 395, DeviceIP: "lhZuppcvFQ", ZoneIP: "bOKsCITBaR", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 380, MaxInBps: 360}
	remark5 := "lcyuXFZtrs"
	bulkData5 := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, AttackStatus: 387, DeviceIP: "GsPIDWSGWW", ZoneIP: "FAmVeHAaAH", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 343, MaxInBps: 343}
	bulkDatas := [...]ent.WofangAlert{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.DeviceIP
	testCase := &CaseRule{Api: wofangAlertApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		wofangAlertIDs = append(wofangAlertIDs, getDataMapId(v))
	}
}

// Query WofangAlert test case
// 根据指定字段、时间范围查询或搜索
func TestQueryWofangAlert(t *testing.T) {
	successExpectedResult.ResponseData = testWofangAlert.DeviceIP
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID WofangAlert test case
// 根据 ID 查询
func TestQueryByIDWofangAlert(t *testing.T) {
	successExpectedResult.ResponseData = testWofangAlert.DeviceIP
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "GET", UrlData: strconv.Itoa(wofangAlertIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID WofangAlert not exist test case
// 根据 ID 查询
func TestQueryByIDWofangAlertNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testWofangAlert.DeviceIP
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query WofangAlert by DeviceIP test case
// 根据指定字段、时间范围查询或搜索
func TestQueryWofangAlertByDeviceIP(t *testing.T) {
	successExpectedResult.ResponseData = testWofangAlert.DeviceIP
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "GET", UrlData: wofangalert.FieldDeviceIP + "=" + testWofangAlert.DeviceIP, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch WofangAlert search by DeviceIP test case
// 分页搜索
func TestQuerySearchWofangAlertDeviceIP(t *testing.T) {
	successExpectedResult.ResponseData = testWofangAlert.DeviceIP
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "GET", UrlData: "search=" + testWofangAlert.DeviceIP, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID WofangAlert test case
// 根据 ID 修改
func TestUpdateByIDWofangAlert(t *testing.T) {
	remark13 := "TSUwPsPVHu"
	updateData := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark13, AttackStatus: 360, DeviceIP: "rZmlkBIPxp", ZoneIP: "ZdLaFlYFar", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 31, MaxInBps: 311}
	successExpectedResult.ResponseData = updateData.DeviceIP
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "PUT", UrlData: strconv.Itoa(wofangAlertIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID WofangAlert test case
// 根据 ID 删除
func TestDeleteByIDWofangAlert(t *testing.T) {
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(wofangAlertIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID WofangAlert not exist test case
// 根据 ID 删除
func TestDeleteByIDWofangAlertNoExist(t *testing.T) {
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk WofangAlert test case
// 根据 IDs 批量删除
func TestDeleteBulkWofangAlert(t *testing.T) {
	testCase := &CaseRule{Api: wofangAlertApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: wofangAlertIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
