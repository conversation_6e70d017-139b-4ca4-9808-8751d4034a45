/**
* <AUTHOR>
* @date 2023-03-13 11:47
* @description
 */

package middleware

import (
	"github.com/gofiber/fiber/v2"
	"github.com/google/wire"
	"meta/app/entity/config"
	"meta/pkg/common"
)

var ApiKeySet = wire.NewSet(wire.Struct(new(Apikey), "*"))

type Apikey struct {
}

func (a *Apikey) AuthSocGroupApiKey() fiber.Handler {
	return func(c *fiber.Ctx) error {
		apiKey := c.Get("x-api-key")
		if apiKey == "" {
			return common.NewErrorWithStatusCode(c, "No api key provided", fiber.StatusUnauthorized)
		}
		if apiKey != config.CFG.External.Service.SocGroupApiKey {
			return common.NewErrorWithStatusCode(c, "Invalid api key", fiber.StatusUnauthorized)
		}
		return c.Next()
	}
}
