package register

import (
	"fmt"
	"meta/app/entity/netease"
	"meta/pkg/common"
	"meta/pkg/common/cache"
	"meta/pkg/http"
	"strings"
)

func (r *Runner) MockWofangDrain() {
	ctx := r.AuthEnt.GetSAContext()

	// fresh cache

	systemConfig, _ := cache.GetSystemConfigCache(r.Rdb, r.SystemConfigService, ctx)
	ip := systemConfig.WofangTestIP
	if ip == "" {
		return
	}
	r.Logger.Sugar().Info("测试沃防牵引接口")

	var projectID int
	project := r.NdsController.ProjectCtl.GetRealProject(ctx, ip)
	if project != nil {
		projectID = project.ID
	}
	_, _, msg, errInfo := r.NdsController.DragWoFangClean(ctx, ip, projectID, "测试沃防接口模块", 60, true)
	if msg != "" {
		if msg == "失败" && strings.Contains(systemConfig.NotifyScenes, "沃防牵引接口测试失败") {
			// 沃防：失败 11
			fmt.Println("失败", ip, errInfo)
			r.Logger.Sugar().Errorf("测试沃防牵引接口失败：%s %s", ip, errInfo)

			notifyData := &netease.NotifyData{
				Prefix:  "【告警提升】" + common.Prefix,
				Status:  "失败",
				Title:   fmt.Sprintf("%s【沃防牵引接口测试失败】", common.Prefix),
				Message: errInfo,
			}
			content, err := common.ParseTemplate("commonNotify", common.NotifyTemplate, notifyData)
			if err != nil {
				fmt.Println(err)
			}
			// 电话通知没有详情，需要发一份popo消息
			err = http.NotifyPhone(content, strings.Split(systemConfig.NotifyPhones, ","))
			err = http.NotifyPopo(content, strings.Split(systemConfig.NotifyEmails, ","))
			if err != nil {
				r.Logger.Sugar().Error(err)
			}
		}
	}
}
