/**
* <AUTHOR>
* @date 2023-12-14 16:56
* @description AliCloud Origin Attack Data Kafka Consumer
 */
package register

import (
	"context"
	"fmt"
	"meta/app/ent"
	"meta/app/entity/alicloud"
	"meta/pkg/common/project"
	"strings"
	"time"

	"github.com/IBM/sarama"
	"github.com/bytedance/sonic"
)

type AliCloudHandler struct {
	Runner *Runner
}

func (h *AliCloudHandler) Setup(sarama.ConsumerGroupSession) error {
	return nil
}

func (h *AliCloudHandler) Cleanup(sarama.ConsumerGroupSession) error {
	return nil
}

// ConsumeClaim must start a consumer loop of ConsumerGroupClaim's Messages().
func (h *AliCloudHandler) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for msg := range claim.Messages() {
		if msg.Value != nil {
			fmt.Println(time.Now().Local())
			fmt.Println("AliCloud Origin Attack Data msg.Value====> ", string(msg.Value))
			go h.ConsumeAliCloudData(msg.Value, h.Runner)
		}
		go session.MarkMessage(msg, "")
	}
	return nil
}

func AliCloudSaramaConsumerGroup(r *Runner) {
	saramaConfig := sarama.NewConfig()
	saramaConfig.Consumer.Return.Errors = false
	saramaConfig.Version = sarama.V0_10_2_0                     // specify appropriate version
	saramaConfig.Consumer.Offsets.Initial = sarama.OffsetNewest // 未找到组消费位移的时候从哪边开始消费

	// AliCloud Kafka配置
	broker := "test01.brokers.canal.netease.com:9092"
	topics := []string{"alicloud_origin_ddos_attack_data_skyline2dep251_test"}
	groupId := "alicloud_origin_attack_data_consumer_group"

	group, err := sarama.NewConsumerGroup([]string{broker}, groupId, saramaConfig)
	if err != nil {
		fmt.Println("AliCloud kafka", err)
		r.Logger.Sugar().Error(err)
	}
	defer func() {
		fmt.Println("AliCloud Consumer group close ")
		_ = group.Close()
	}()

	// Track errors
	go func() {
		for err := range group.Errors() {
			fmt.Println("AliCloud ERROR", err)
		}
	}()
	fmt.Println("AliCloud Consumer start")
	// Iterate over consumer sessions.
	ctx := context.Background()
	for {
		handler := &AliCloudHandler{Runner: r}
		// `Consume` should be called inside an infinite loop, when a
		// server-side rebalance happens, the consumer session will need to be
		// recreated to get the new claims
		err := group.Consume(ctx, topics, handler)
		if err != nil {
			fmt.Println("AliCloud kafka", err)
			r.Logger.Sugar().Error(err)
		}
	}
}

func (h *AliCloudHandler) ConsumeAliCloudData(data []byte, r *Runner) {
	kafkaMessage := &alicloud.KafkaMessage{}
	err := sonic.Unmarshal(data, &kafkaMessage)
	if err != nil {
		r.Logger.Sugar().Error("AliCloud unmarshal error:", err)
		return
	}

	ctx := r.AuthEnt.GetSAContext()

	for _, attackData := range kafkaMessage.Data {
		r.Logger.Sugar().Infof("Processing AliCloud attack data: %+v", attackData)

		// 转换为ent实体
		entAttackData := h.parseAliCloudAttackData(&attackData)

		// 根据项目代码获取租户信息
		code := attackData.Project
		ip := attackData.IP

		if code == "" || strings.ToLower(code) == "unknown" {
			code = "cld"
		}

		galaxyProject := project.GetProjectByCodeOrIP(code, ip, ctx, r.NdsController)
		if galaxyProject != nil {
			entAttackData.TenantID = &galaxyProject.ID
		}

		// 保存到数据库
		_, err = r.AliCloudOriginAttackDataService.Create(ctx, entAttackData)
		if err != nil {
			r.Logger.Sugar().Errorf("AliCloudOriginAttackDataService.Create failed: %+v, error: %s", entAttackData, err)
		} else {
			r.Logger.Sugar().Infof("Successfully created AliCloud attack data: %+v", entAttackData)
		}
	}
}

func (h *AliCloudHandler) parseAliCloudAttackData(data *alicloud.AliCloudOriginAttackDataMessage) *ent.AliCloudOriginAttackData {
	entData := &ent.AliCloudOriginAttackData{
		EventType: data.EventType,
		Project:   data.Project,
		IP:        data.IP,
		Region:    data.Region,
		Action:    data.Action,
		Time:      data.Time,
		Pps:       data.Pps,
		Bps:       data.Bps,
		DdosType:  data.DdosType,
	}

	return entData
}

func (r *Runner) FetchAliCloudOriginAttackData() {
	AliCloudSaramaConsumerGroup(r)
}
