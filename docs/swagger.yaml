definitions:
  chart.LineData:
    properties:
      name:
        type: string
      time:
        type: string
      value:
        type: integer
    type: object
  common.DeleteItem:
    properties:
      ids:
        items:
          type: integer
        type: array
    type: object
  common.Message:
    properties:
      message:
        type: string
      success:
        type: boolean
    type: object
  common.Result:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
      success:
        type: boolean
      total:
        type: integer
    type: object
  ent.AliCloudOriginAttackData:
    properties:
      cloud_type:
        description: 云类型
        type: string
      created_at:
        description: 创建时间
        type: string
      duration:
        description: 攻击持续时间，单位秒
        type: integer
      edges:
        allOf:
        - $ref: '#/definitions/ent.AliCloudOriginAttackDataEdges'
        description: |-
          Edges holds the relations/edges for other nodes in the graph.
          The values are being populated by the AliCloudOriginAttackDataQuery when eager-loading is set.
      end_time:
        description: 结束时间
        type: string
      id:
        description: ID of the ent.
        type: integer
      ip:
        description: 被攻击IP
        type: string
      mbps:
        description: 攻击开始时刻的请求流量大小
        type: integer
      pps:
        description: 攻击开始时刻的报文数量大小
        type: integer
      project:
        description: IP 所属项目
        type: string
      remark:
        description: 备注
        type: string
      start_time:
        description: 攻击开始时间
        type: string
      status:
        description: 攻击事件的当前状态
        type: string
      tenant_id:
        description: 租户Id，可选
        type: integer
      updated_at:
        description: 更新时间
        type: string
    type: object
  ent.AliCloudOriginAttackDataEdges:
    properties:
      tenant:
        allOf:
        - $ref: '#/definitions/ent.Tenant'
        description: Tenant holds the value of the tenant edge.
    type: object
  ent.CasbinRule:
    properties:
      act:
        description: Act holds the value of the "act" field.
        type: string
      dom:
        description: Dom holds the value of the "dom" field.
        type: string
      id:
        description: ID of the ent.
        type: integer
      obj:
        description: Obj holds the value of the "obj" field.
        type: string
      sub:
        description: Sub holds the value of the "sub" field.
        type: string
      type:
        description: Type holds the value of the "type" field.
        type: string
    type: object
  ent.CleanData:
    properties:
      attack_flags:
        description: AttackFlags holds the value of the "attack_flags" field.
        type: integer
      c_filter:
        description: CFilter holds the value of the "c_filter" field.
        type: string
      c_filter_id:
        description: CFilterID holds the value of the "c_filter_id" field.
        type: integer
      count:
        description: Count holds the value of the "count" field.
        type: integer
      created_at:
        description: 创建时间
        type: string
      edges:
        allOf:
        - $ref: '#/definitions/ent.CleanDataEdges'
        description: |-
          Edges holds the relations/edges for other nodes in the graph.
          The values are being populated by the CleanDataQuery when eager-loading is set.
      host:
        description: Host holds the value of the "host" field.
        type: string
      id:
        description: ID of the ent.
        type: integer
      in_ack_bps:
        description: InAckBps holds the value of the "in_ack_bps" field.
        type: integer
      in_ack_pps:
        description: InAckPps holds the value of the "in_ack_pps" field.
        type: integer
      in_bps:
        description: InBps holds the value of the "in_bps" field.
        type: integer
      in_dns_bps:
        description: InDNSBps holds the value of the "in_dns_bps" field.
        type: integer
      in_dns_pps:
        description: InDNSPps holds the value of the "in_dns_pps" field.
        type: integer
      in_icmp_bps:
        description: InIcmpBps holds the value of the "in_icmp_bps" field.
        type: integer
      in_icmp_pps:
        description: InIcmpPps holds the value of the "in_icmp_pps" field.
        type: integer
      in_pps:
        description: InPps holds the value of the "in_pps" field.
        type: integer
      in_syn_pps:
        description: InSynPps holds the value of the "in_syn_pps" field.
        type: integer
      in_udp_bps:
        description: InUDPBps holds the value of the "in_udp_bps" field.
        type: integer
      in_udp_pps:
        description: InUDPPps holds the value of the "in_udp_pps" field.
        type: integer
      ip:
        description: IP holds the value of the "ip" field.
        type: string
      ip_type:
        description: IPType holds the value of the "ip_type" field.
        type: integer
      out_ack_bps:
        description: OutAckBps holds the value of the "out_ack_bps" field.
        type: integer
      out_ack_pps:
        description: OutAckPps holds the value of the "out_ack_pps" field.
        type: integer
      out_bps:
        description: OutBps holds the value of the "out_bps" field.
        type: integer
      out_dns_bps:
        description: OutDNSBps holds the value of the "out_dns_bps" field.
        type: integer
      out_dns_pps:
        description: OutDNSPps holds the value of the "out_dns_pps" field.
        type: integer
      out_icmp_bps:
        description: OutIcmpBps holds the value of the "out_icmp_bps" field.
        type: integer
      out_icmp_pps:
        description: OutIcmpPps holds the value of the "out_icmp_pps" field.
        type: integer
      out_pps:
        description: OutPps holds the value of the "out_pps" field.
        type: integer
      out_syn_pps:
        description: OutSynPps holds the value of the "out_syn_pps" field.
        type: integer
      out_udp_bps:
        description: OutUDPBps holds the value of the "out_udp_bps" field.
        type: integer
      out_udp_pps:
        description: OutUDPPps holds the value of the "out_udp_pps" field.
        type: integer
      spectrum_alert_id:
        description: SpectrumAlertID holds the value of the "spectrum_alert_id" field.
        type: integer
      tenant_id:
        description: 租户Id，可选
        type: integer
      time:
        description: Time holds the value of the "time" field.
        type: string
    type: object
  ent.CleanDataEdges:
    properties:
      spectrum_alert:
        allOf:
        - $ref: '#/definitions/ent.SpectrumAlert'
        description: SpectrumAlert holds the value of the spectrum_alert edge.
      tenant:
        allOf:
        - $ref: '#/definitions/ent.Tenant'
        description: Tenant holds the value of the tenant edge.
    type: object
  ent.CloudAlert:
    properties:
      created_at:
        description: 创建时间
        type: string
      defence_level:
        description: 防护水平
        type: integer
      defence_mode:
        description: 清洗模式
        type: integer
      dst_ip:
        description: 被攻击IP
        type: string
      dst_port:
        description: 被攻击端口
        type: integer
      edges:
        allOf:
        - $ref: '#/definitions/ent.CloudAlertEdges'
        description: |-
          Edges holds the relations/edges for other nodes in the graph.
          The values are being populated by the CloudAlertQuery when eager-loading is set.
      end_time:
        description: 攻击结束时间
        type: string
      flow_mode:
        description: 流匹配模式
        type: integer
      id:
        description: ID of the ent.
        type: integer
      max_attack_pps:
        description: 从攻击开始到攻击结束，最大的pps
        type: integer
      max_pps:
        description: 最大攻击PPS
        type: integer
      overlimit_pkt_count:
        description: 超出阈值的报文总数
        type: integer
      protocol:
        description: 4层协议：6tcp，17udp
        type: integer
      remark:
        description: 备注
        type: string
      src_ip:
        description: 来源IP
        type: string
      src_port:
        description: 来源端口
        type: integer
      start_time:
        description: 攻击开始时间
        type: string
      tcp_ack_num:
        description: TCP报文中的ACK值
        type: string
      tcp_seq_num:
        description: TCP报文中的SEQ值
        type: string
      tenant_id:
        description: 租户Id，可选
        type: integer
      updated_at:
        description: 更新时间
        type: string
    type: object
  ent.CloudAlertEdges:
    properties:
      cloudflow_datas:
        description: CloudflowDatas holds the value of the cloudflow_datas edge.
        items:
          $ref: '#/definitions/ent.CloudFlowData'
        type: array
      tenant:
        allOf:
        - $ref: '#/definitions/ent.Tenant'
        description: Tenant holds the value of the tenant edge.
    type: object
  ent.CloudAttackData:
    properties:
      created_at:
        description: 创建时间
        type: string
      current_attack_pps:
        description: 这一秒pps
        type: integer
      dst_ip:
        description: 被攻击IP
        type: string
      dst_port:
        description: 被攻击端口
        type: integer
      edges:
        allOf:
        - $ref: '#/definitions/ent.CloudAttackDataEdges'
        description: |-
          Edges holds the relations/edges for other nodes in the graph.
          The values are being populated by the CloudAttackDataQuery when eager-loading is set.
      end_time:
        description: 结束时间
        type: string
      id:
        description: ID of the ent.
        type: integer
      protocol:
        description: 4层协议：6tcp，17udp
        type: integer
      remark:
        description: 备注
        type: string
      src_ip:
        description: 来源IP
        type: string
      src_port:
        description: 来源端口
        type: integer
      start_time:
        description: 攻击开始时间
        type: string
      tenant_id:
        description: 租户Id，可选
        type: integer
      updated_at:
        description: 更新时间
        type: string
    type: object
  ent.CloudAttackDataEdges:
    properties:
      tenant:
        allOf:
        - $ref: '#/definitions/ent.Tenant'
        description: Tenant holds the value of the tenant edge.
    type: object
  ent.CloudFlowData:
    properties:
      cloud_alert_id:
        description: CloudAlertID holds the value of the "cloud_alert_id" field.
        type: integer
      created_at:
        description: 创建时间
        type: string
      dst_ip:
        description: 被攻击IP
        type: string
      dst_port:
        description: 被攻击端口
        type: integer
      edges:
        allOf:
        - $ref: '#/definitions/ent.CloudFlowDataEdges'
        description: |-
          Edges holds the relations/edges for other nodes in the graph.
          The values are being populated by the CloudFlowDataQuery when eager-loading is set.
      end_time:
        description: 上一个报文到达的时间
        type: string
      flow_over_max_pps_count:
        description: 总pps>1000的次数
        type: integer
      id:
        description: ID of the ent.
        type: integer
      max_attack_pps:
        description: 从攻击开始到攻击结束，最大的pps
        type: integer
      protocol:
        description: 4层协议：6tcp，17udp
        type: integer
      remark:
        description: 备注
        type: string
      src_ip:
        description: 来源IP
        type: string
      src_port:
        description: 来源端口
        type: integer
      start_time:
        description: 攻击开始时间
        type: string
      tenant_id:
        description: 租户Id，可选
        type: integer
      updated_at:
        description: 更新时间
        type: string
    type: object
  ent.CloudFlowDataEdges:
    properties:
      cloud_alert:
        allOf:
        - $ref: '#/definitions/ent.CloudAlert'
        description: CloudAlert holds the value of the cloud_alert edge.
      tenant:
        allOf:
        - $ref: '#/definitions/ent.Tenant'
        description: Tenant holds the value of the tenant edge.
    type: object
  ent.DataSync:
    properties:
      created_at:
        description: 创建时间
        type: string
      data_list:
        description: DataList holds the value of the "data_list" field.
        items:
          type: string
        type: array
      data_type:
        description: DataType holds the value of the "data_type" field.
        type: string
      id:
        description: ID of the ent.
        type: integer
      pre_data_list:
        description: PreDataList holds the value of the "pre_data_list" field.
        items:
          type: string
        type: array
      remark:
        description: 备注
        type: string
      type:
        description: Type holds the value of the "type" field.
        type: string
      updated_at:
        description: 更新时间
        type: string
    type: object
  ent.Group:
    properties:
      edges:
        allOf:
        - $ref: '#/definitions/ent.GroupEdges'
        description: |-
          Edges holds the relations/edges for other nodes in the graph.
          The values are being populated by the GroupQuery when eager-loading is set.
      id:
        description: ID of the ent.
        type: integer
      name:
        description: Name holds the value of the "name" field.
        type: string
      tenant_id:
        description: 租户Id，可选
        type: integer
    type: object
  ent.GroupEdges:
    properties:
      tenant:
        allOf:
        - $ref: '#/definitions/ent.Tenant'
        description: Tenant holds the value of the tenant edge.
      users:
        description: Users holds the value of the users edge.
        items:
          $ref: '#/definitions/ent.User'
        type: array
    type: object
  ent.MatrixSpectrumAlert:
    properties:
      attack_info:
        allOf:
        - $ref: '#/definitions/netease.MatrixAttackInfo'
        description: AttackInfo holds the value of the "attack_info" field.
      attack_type:
        description: 攻击类型
        type: string
      bps:
        description: 告警BPS
        type: integer
      created_at:
        description: 创建时间
        type: string
      edges:
        allOf:
        - $ref: '#/definitions/ent.MatrixSpectrumAlertEdges'
        description: |-
          Edges holds the relations/edges for other nodes in the graph.
          The values are being populated by the MatrixSpectrumAlertQuery when eager-loading is set.
      end_time:
        description: 攻击结束时间
        type: string
      id:
        description: ID of the ent.
        type: integer
      ip_list:
        description: IPList holds the value of the "ip_list" field.
        items:
          type: string
        type: array
      isp:
        description: Isp holds the value of the "isp" field.
        type: string
      matrix_strategy_id:
        description: MatrixStrategyID holds the value of the "matrix_strategy_id"
          field.
        type: integer
      net_type:
        description: NetType holds the value of the "net_type" field.
        type: string
      region:
        description: Region holds the value of the "region" field.
        type: string
      remark:
        description: 备注
        type: string
      start_time:
        description: 攻击开始时间
        type: string
      tenant_id:
        description: 租户Id，可选
        type: integer
      updated_at:
        description: 更新时间
        type: string
      wofang_id:
        description: WofangID holds the value of the "wofang_id" field.
        type: integer
    type: object
  ent.MatrixSpectrumAlertEdges:
    properties:
      matrix_spectrum_datas:
        description: MatrixSpectrumDatas holds the value of the matrix_spectrum_datas
          edge.
        items:
          $ref: '#/definitions/ent.MatrixSpectrumData'
        type: array
      matrix_strategy:
        allOf:
        - $ref: '#/definitions/ent.MatrixStrategy'
        description: MatrixStrategy holds the value of the matrix_strategy edge.
      tenant:
        allOf:
        - $ref: '#/definitions/ent.Tenant'
        description: Tenant holds the value of the tenant edge.
      wofang_ticket:
        allOf:
        - $ref: '#/definitions/ent.Wofang'
        description: WofangTicket holds the value of the wofang_ticket edge.
    type: object
  ent.MatrixSpectrumData:
    properties:
      bps:
        description: Bps holds the value of the "bps" field.
        type: integer
      created_at:
        description: 创建时间
        type: string
      edges:
        allOf:
        - $ref: '#/definitions/ent.MatrixSpectrumDataEdges'
        description: |-
          Edges holds the relations/edges for other nodes in the graph.
          The values are being populated by the MatrixSpectrumDataQuery when eager-loading is set.
      id:
        description: ID of the ent.
        type: integer
      isp:
        description: Isp holds the value of the "isp" field.
        type: string
      matrix_spectrum_alert_id:
        description: MatrixSpectrumAlertID holds the value of the "matrix_spectrum_alert_id"
          field.
        type: integer
      net_type:
        description: NetType holds the value of the "net_type" field.
        type: string
      region:
        description: Region holds the value of the "region" field.
        type: string
      tenant_id:
        description: 租户Id，可选
        type: integer
      time:
        description: Time holds the value of the "time" field.
        type: string
      updated_at:
        description: 更新时间
        type: string
    type: object
  ent.MatrixSpectrumDataEdges:
    properties:
      matrix_spectrum_alert:
        allOf:
        - $ref: '#/definitions/ent.MatrixSpectrumAlert'
        description: MatrixSpectrumAlert holds the value of the matrix_spectrum_alert
          edge.
      tenant:
        allOf:
        - $ref: '#/definitions/ent.Tenant'
        description: Tenant holds the value of the tenant edge.
    type: object
  ent.MatrixStrategy:
    properties:
      created_at:
        description: 创建时间
        type: string
      drag_bps:
        description: DragBps holds the value of the "drag_bps" field.
        type: integer
      drag_type:
        description: DragType holds the value of the "drag_type" field.
        type: integer
      edges:
        allOf:
        - $ref: '#/definitions/ent.MatrixStrategyEdges'
        description: |-
          Edges holds the relations/edges for other nodes in the graph.
          The values are being populated by the MatrixStrategyQuery when eager-loading is set.
      id:
        description: ID of the ent.
        type: integer
      isp:
        description: Isp holds the value of the "isp" field.
        type: string
      monitor_bps:
        description: MonitorBps holds the value of the "monitor_bps" field.
        type: integer
      name:
        description: Name holds the value of the "name" field.
        type: string
      net_type:
        description: NetType holds the value of the "net_type" field.
        type: string
      region:
        description: Region holds the value of the "region" field.
        type: string
      remark:
        description: 备注
        type: string
      updated_at:
        description: 更新时间
        type: string
    type: object
  ent.MatrixStrategyEdges:
    properties:
      matrix_strategy_alerts:
        description: MatrixStrategyAlerts holds the value of the matrix_strategy_alerts
          edge.
        items:
          $ref: '#/definitions/ent.MatrixSpectrumAlert'
        type: array
    type: object
  ent.Notify:
    properties:
      created_at:
        description: 创建时间
        type: string
      edges:
        allOf:
        - $ref: '#/definitions/ent.NotifyEdges'
        description: |-
          Edges holds the relations/edges for other nodes in the graph.
          The values are being populated by the NotifyQuery when eager-loading is set.
      email:
        description: true，通过邮件对emails进行通知
        type: boolean
      emails:
        description: 邮件列表
        items:
          type: string
        type: array
      enabled:
        description: 是否启用
        type: boolean
      id:
        description: ID of the ent.
        type: integer
      ip_whitelists:
        description: 通知IP白名单，告警IP在白名单中将不再通知；项目白名单 > 系统设置白名单
        items:
          type: string
        type: array
      name:
        description: 名称
        type: string
      phone:
        description: true，通过电话对phones进行通知
        type: boolean
      phones:
        description: 电话列表
        items:
          type: string
        type: array
      popo:
        description: true，通过popo对emails进行通知
        type: boolean
      popo_groups:
        description: 通过popo群进行通知
        items:
          type: string
        type: array
      remark:
        description: 备注
        type: string
      sa_notify_email:
        description: true，通过auth获取sa列表，并通过邮件进行通知；仅非系统配置生效
        type: boolean
      sa_notify_popo:
        description: true，通过auth获取sa列表，并通过popo进行通知；仅非系统配置生效
        type: boolean
      sms:
        description: true，通过短信对phones进行通知
        type: boolean
      system:
        description: true，系统配置
        type: boolean
      tenant_id:
        description: 租户Id，可选
        type: integer
      updated_at:
        description: 更新时间
        type: string
    type: object
  ent.NotifyEdges:
    properties:
      tenant:
        allOf:
        - $ref: '#/definitions/ent.Tenant'
        description: Tenant holds the value of the tenant edge.
    type: object
  ent.ProtectGroup:
    properties:
      created_at:
        description: 创建时间
        type: string
      drag_info:
        allOf:
        - $ref: '#/definitions/netease.DragInfo'
        description: DragInfo holds the value of the "drag_info" field.
      edges:
        allOf:
        - $ref: '#/definitions/ent.ProtectGroupEdges'
        description: |-
          Edges holds the relations/edges for other nodes in the graph.
          The values are being populated by the ProtectGroupQuery when eager-loading is set.
      expand_ip:
        description: ExpandIP holds the value of the "expand_ip" field.
        type: string
      group_id:
        description: GroupID holds the value of the "group_id" field.
        type: integer
      group_name:
        description: GroupName holds the value of the "group_name" field.
        type: string
      id:
        description: ID of the ent.
        type: integer
      ip_list:
        description: IPList holds the value of the "ip_list" field.
        items:
          type: string
        type: array
      monitor_info:
        allOf:
        - $ref: '#/definitions/netease.MonitorInfo'
        description: MonitorInfo holds the value of the "monitor_info" field.
      nds4_config:
        allOf:
        - $ref: '#/definitions/netease.Nds4Config'
        description: Nds4Config holds the value of the "nds4_config" field.
      nds6_config:
        allOf:
        - $ref: '#/definitions/netease.Nds6Config'
        description: Nds6Config holds the value of the "nds6_config" field.
      remark:
        description: 备注
        type: string
      tenant_id:
        description: 租户Id，可选
        type: integer
      type:
        description: Type holds the value of the "type" field.
        type: integer
      updated_at:
        description: 更新时间
        type: string
    type: object
  ent.ProtectGroupEdges:
    properties:
      spectrum_alerts:
        description: SpectrumAlerts holds the value of the spectrum_alerts edge.
        items:
          $ref: '#/definitions/ent.SpectrumAlert'
        type: array
      tenant:
        allOf:
        - $ref: '#/definitions/ent.Tenant'
        description: Tenant holds the value of the tenant edge.
    type: object
  ent.SkylineDos:
    properties:
      attack_counters:
        description: AttackCounters holds the value of the "attack_counters" field.
        items:
          $ref: '#/definitions/gamecloud.AttackCounter'
        type: array
      attack_id:
        description: AttackID holds the value of the "attack_id" field.
        type: string
      created_at:
        description: 创建时间
        type: string
      duration_time:
        description: DurationTime holds the value of the "duration_time" field.
        type: integer
      edges:
        allOf:
        - $ref: '#/definitions/ent.SkylineDosEdges'
        description: |-
          Edges holds the relations/edges for other nodes in the graph.
          The values are being populated by the SkylineDosQuery when eager-loading is set.
      end_time:
        description: 攻击结束时间
        type: string
      id:
        description: ID of the ent.
        type: integer
      project:
        description: Project holds the value of the "project" field.
        type: string
      region:
        description: Region holds the value of the "region" field.
        type: string
      remark:
        description: 备注
        type: string
      resource:
        description: Resource holds the value of the "resource" field.
        type: string
      resource_type:
        description: ResourceType holds the value of the "resource_type" field.
        type: string
      start_time:
        description: 攻击开始时间
        type: string
      status:
        description: Status holds the value of the "status" field.
        type: string
      tenant_id:
        description: 租户Id，可选
        type: integer
      updated_at:
        description: 更新时间
        type: string
      vector_types:
        description: 攻击类型
        items:
          type: string
        type: array
    type: object
  ent.SkylineDosEdges:
    properties:
      tenant:
        allOf:
        - $ref: '#/definitions/ent.Tenant'
        description: Tenant holds the value of the tenant edge.
    type: object
  ent.SocGroupTicket:
    properties:
      config_args:
        description: 参数配置  仅[清洗上线和清洗调优且configType=2(自定义防护参数)] 有效
        type: string
      config_type:
        description: 参数配置类型，1表示默认，2表示自定义  仅[清洗上线] 有效
        type: integer
      contact_list:
        description: 紧急联系人列表
        items:
          $ref: '#/definitions/socgroup.User'
        type: array
      create_user_id:
        description: 创建用户Id，可选
        type: integer
      created_at:
        description: 创建时间
        type: string
      department_id:
        description: 部门id
        type: integer
      description:
        description: 描述
        type: string
      divert_type:
        description: 清洗方式, 1表示清洗上线，2表示清洗下线 3调优
        type: integer
      edges:
        allOf:
        - $ref: '#/definitions/ent.SocGroupTicketEdges'
        description: |-
          Edges holds the relations/edges for other nodes in the graph.
          The values are being populated by the SocGroupTicketQuery when eager-loading is set.
      error_info:
        description: 错误信息
        type: string
      follow_list:
        description: 工单跟踪者id列表
        items:
          type: integer
        type: array
      group_ticket_id:
        description: 集团工单编号
        type: integer
      id:
        description: ID of the ent.
        type: integer
      ip_list:
        description: 牵引IP列表
        items:
          type: string
        type: array
      min_bandwidth:
        description: 牵引IP列表中最小的物理带宽或压测带宽(非业务流量)，保留两位小数，单位Gbps
        type: number
      name:
        description: 名称
        type: string
      op_time:
        description: 操作时间
        type: string
      op_type:
        description: 操作方式，1表示自动，2表示手动
        type: integer
      product_code:
        description: 产品代号
        type: string
      product_name:
        description: 产品中文名
        type: string
      remark:
        description: 备注
        type: string
      tenant_id:
        description: 租户Id，可选
        type: integer
      type:
        description: 类型
        type: string
      updated_at:
        description: 更新时间
        type: string
    type: object
  ent.SocGroupTicketEdges:
    properties:
      tenant:
        allOf:
        - $ref: '#/definitions/ent.Tenant'
        description: Tenant holds the value of the tenant edge.
      user:
        allOf:
        - $ref: '#/definitions/ent.User'
        description: User holds the value of the user edge.
    type: object
  ent.SpectrumAlert:
    properties:
      attack_info:
        allOf:
        - $ref: '#/definitions/netease.AttackInfo'
        description: AttackInfo holds the value of the "attack_info" field.
      attack_type:
        description: 攻击类型
        type: string
      created_at:
        description: 创建时间
        type: string
      edges:
        allOf:
        - $ref: '#/definitions/ent.SpectrumAlertEdges'
        description: |-
          Edges holds the relations/edges for other nodes in the graph.
          The values are being populated by the SpectrumAlertQuery when eager-loading is set.
      end_time:
        description: 攻击结束时间
        type: string
      id:
        description: ID of the ent.
        type: integer
      ip:
        description: 被攻击IP
        type: string
      isp_code:
        description: IspCode holds the value of the "isp_code" field.
        type: integer
      max_bps:
        description: 告警BPS
        type: integer
      max_pps:
        description: 告警PPS
        type: integer
      protect_group_id:
        description: ProtectGroupID holds the value of the "protect_group_id" field.
        type: integer
      protect_status:
        description: ProtectStatus holds the value of the "protect_status" field.
        items:
          type: integer
        type: array
      remark:
        description: 备注
        type: string
      start_time:
        description: 攻击开始时间
        type: string
      strategy_id:
        description: StrategyID holds the value of the "strategy_id" field.
        type: integer
      tenant_id:
        description: 租户Id，可选
        type: integer
      updated_at:
        description: 更新时间
        type: string
      wofang_id:
        description: WofangID holds the value of the "wofang_id" field.
        type: integer
    type: object
  ent.SpectrumAlertEdges:
    properties:
      clean_datas:
        description: CleanDatas holds the value of the clean_datas edge.
        items:
          $ref: '#/definitions/ent.CleanData'
        type: array
      protect_group:
        allOf:
        - $ref: '#/definitions/ent.ProtectGroup'
        description: ProtectGroup holds the value of the protect_group edge.
      spectrum_datas:
        description: SpectrumDatas holds the value of the spectrum_datas edge.
        items:
          $ref: '#/definitions/ent.SpectrumData'
        type: array
      strategy:
        allOf:
        - $ref: '#/definitions/ent.Strategy'
        description: Strategy holds the value of the strategy edge.
      tenant:
        allOf:
        - $ref: '#/definitions/ent.Tenant'
        description: Tenant holds the value of the tenant edge.
      wofang_ticket:
        allOf:
        - $ref: '#/definitions/ent.Wofang'
        description: WofangTicket holds the value of the wofang_ticket edge.
    type: object
  ent.SpectrumData:
    properties:
      ack_bps:
        description: AckBps holds the value of the "ack_bps" field.
        type: integer
      ack_pps:
        description: AckPps holds the value of the "ack_pps" field.
        type: integer
      bps:
        description: Bps holds the value of the "bps" field.
        type: integer
      created_at:
        description: 创建时间
        type: string
      data_type:
        description: DataType holds the value of the "data_type" field.
        type: integer
      dns_answer_bps:
        description: DNSAnswerBps holds the value of the "dns_answer_bps" field.
        type: integer
      dns_answer_pps:
        description: DNSAnswerPps holds the value of the "dns_answer_pps" field.
        type: integer
      dns_query_bps:
        description: DNSQueryBps holds the value of the "dns_query_bps" field.
        type: integer
      dns_query_pps:
        description: DNSQueryPps holds the value of the "dns_query_pps" field.
        type: integer
      edges:
        allOf:
        - $ref: '#/definitions/ent.SpectrumDataEdges'
        description: |-
          Edges holds the relations/edges for other nodes in the graph.
          The values are being populated by the SpectrumDataQuery when eager-loading is set.
      host:
        description: Host holds the value of the "host" field.
        type: string
      icmp_bps:
        description: IcmpBps holds the value of the "icmp_bps" field.
        type: integer
      icmp_pps:
        description: IcmpPps holds the value of the "icmp_pps" field.
        type: integer
      id:
        description: ID of the ent.
        type: integer
      ip:
        description: IP holds the value of the "ip" field.
        type: string
      ip_type:
        description: IPType holds the value of the "ip_type" field.
        type: integer
      monitor:
        description: Monitor holds the value of the "monitor" field.
        type: string
      monitor_id:
        description: MonitorID holds the value of the "monitor_id" field.
        type: integer
      ntp_bps:
        description: NtpBps holds the value of the "ntp_bps" field.
        type: integer
      ntp_pps:
        description: NtpPps holds the value of the "ntp_pps" field.
        type: integer
      pps:
        description: Pps holds the value of the "pps" field.
        type: integer
      product:
        description: Product holds the value of the "product" field.
        type: string
      qps:
        description: QPS holds the value of the "qps" field.
        type: integer
      receive_count:
        description: ReceiveCount holds the value of the "receive_count" field.
        type: integer
      small_pps:
        description: SmallPps holds the value of the "small_pps" field.
        type: integer
      spectrum_alert_id:
        description: SpectrumAlertID holds the value of the "spectrum_alert_id" field.
        type: integer
      ssdp_bps:
        description: SsdpBps holds the value of the "ssdp_bps" field.
        type: integer
      ssdp_pps:
        description: SsdpPps holds the value of the "ssdp_pps" field.
        type: integer
      syn_ack_bps:
        description: SynAckBps holds the value of the "syn_ack_bps" field.
        type: integer
      syn_ack_pps:
        description: SynAckPps holds the value of the "syn_ack_pps" field.
        type: integer
      syn_bps:
        description: SynBps holds the value of the "syn_bps" field.
        type: integer
      syn_pps:
        description: SynPps holds the value of the "syn_pps" field.
        type: integer
      tenant_id:
        description: 租户Id，可选
        type: integer
      time:
        description: Time holds the value of the "time" field.
        type: string
      udp_bps:
        description: UDPBps holds the value of the "udp_bps" field.
        type: integer
      udp_pps:
        description: UDPPps holds the value of the "udp_pps" field.
        type: integer
    type: object
  ent.SpectrumDataEdges:
    properties:
      spectrum_alert:
        allOf:
        - $ref: '#/definitions/ent.SpectrumAlert'
        description: SpectrumAlert holds the value of the spectrum_alert edge.
      tenant:
        allOf:
        - $ref: '#/definitions/ent.Tenant'
        description: Tenant holds the value of the tenant edge.
    type: object
  ent.Strategy:
    properties:
      bps:
        description: 总bps大小阈值
        type: integer
      bps_count:
        description: bps次数阈值
        type: integer
      created_at:
        description: 创建时间
        type: string
      edges:
        allOf:
        - $ref: '#/definitions/ent.StrategyEdges'
        description: |-
          Edges holds the relations/edges for other nodes in the graph.
          The values are being populated by the StrategyQuery when eager-loading is set.
      enabled:
        description: 是否启用
        type: boolean
      id:
        description: ID of the ent.
        type: integer
      isp_code:
        description: ip运营商类型
        type: integer
      name:
        description: 名称
        type: string
      pps:
        description: 总pps大小阈值
        type: integer
      pps_count:
        description: pps次数阈值
        type: integer
      remark:
        description: 备注
        type: string
      system:
        description: 系统策略，项目未配置自定义策略情况下，使用系统策略
        type: boolean
      tenant_id:
        description: 租户Id，可选
        type: integer
      type:
        description: 牵引防护类型
        type: integer
      updated_at:
        description: 更新时间
        type: string
    type: object
  ent.StrategyEdges:
    properties:
      strategy_alerts:
        description: StrategyAlerts holds the value of the strategy_alerts edge.
        items:
          $ref: '#/definitions/ent.SpectrumAlert'
        type: array
      tenant:
        allOf:
        - $ref: '#/definitions/ent.Tenant'
        description: Tenant holds the value of the tenant edge.
    type: object
  ent.SystemApi:
    properties:
      created_at:
        description: 创建时间
        type: string
      http_method:
        description: http方法
        type: string
      id:
        description: ID of the ent.
        type: integer
      name:
        description: 名称
        type: string
      path:
        description: 路径
        type: string
      public:
        description: 是否公共接口，所有用户可以访问
        type: boolean
      remark:
        description: 备注
        type: string
      roles:
        description: 角色
        items:
          type: string
        type: array
      sa:
        description: 是否sa接口，sa接口仅sa用户可操作（如果public，则普通用户可以访问(get）
        type: boolean
      updated_at:
        description: 更新时间
        type: string
    type: object
  ent.SystemConfig:
    properties:
      created_at:
        description: 创建时间
        type: string
      id:
        description: ID of the ent.
        type: integer
      ip_whitelists:
        description: 通知IP白名单，IP在白名单中将不再通知
        items:
          type: string
        type: array
      notify_emails:
        description: NotifyEmails holds the value of the "notify_emails" field.
        items:
          type: string
        type: array
      notify_phones:
        description: NotifyPhones holds the value of the "notify_phones" field.
        items:
          type: string
        type: array
      notify_scenes:
        description: NotifyScenes holds the value of the "notify_scenes" field.
        items:
          type: string
        type: array
      remark:
        description: 备注
        type: string
      updated_at:
        description: 更新时间
        type: string
      wofang_test_ip:
        description: WofangTestIP holds the value of the "wofang_test_ip" field.
        type: string
    type: object
  ent.Tenant:
    properties:
      code:
        description: Code holds the value of the "code" field.
        type: string
      id:
        description: ID of the ent.
        type: integer
      name:
        description: Name holds the value of the "name" field.
        type: string
      offline:
        description: Offline holds the value of the "offline" field.
        type: boolean
    type: object
  ent.User:
    properties:
      created_at:
        description: 创建时间
        type: string
      edges:
        allOf:
        - $ref: '#/definitions/ent.UserEdges'
        description: |-
          Edges holds the relations/edges for other nodes in the graph.
          The values are being populated by the UserQuery when eager-loading is set.
      id:
        description: ID of the ent.
        type: integer
      name:
        description: Name holds the value of the "name" field.
        type: string
      password:
        description: Password holds the value of the "password" field.
        type: string
      super_admin:
        description: 超级管理员，可操作所有租户数据
        type: boolean
      update_auth:
        description: true，则重新增加用户权限（增量）：项目、角色、api资源
        type: boolean
      updated_at:
        description: 更新时间
        type: string
      valid:
        description: 有效
        type: boolean
    type: object
  ent.UserEdges:
    properties:
      groups:
        description: Groups holds the value of the groups edge.
        items:
          $ref: '#/definitions/ent.Group'
        type: array
    type: object
  ent.UserOperationLog:
    properties:
      created_at:
        description: 创建时间
        type: string
      id:
        description: ID of the ent.
        type: integer
      method:
        description: Method holds the value of the "method" field.
        type: string
      project:
        description: Project holds the value of the "project" field.
        type: string
      remark:
        description: 备注
        type: string
      request_body:
        description: RequestBody holds the value of the "request_body" field.
        type: string
      request_id:
        description: RequestID holds the value of the "request_id" field.
        type: string
      updated_at:
        description: 更新时间
        type: string
      uri:
        description: URI holds the value of the "uri" field.
        type: string
      username:
        description: Username holds the value of the "username" field.
        type: string
    type: object
  ent.Wofang:
    properties:
      create_user_id:
        description: 创建用户Id，可选
        type: integer
      created_at:
        description: 创建时间
        type: string
      edges:
        allOf:
        - $ref: '#/definitions/ent.WofangEdges'
        description: |-
          Edges holds the relations/edges for other nodes in the graph.
          The values are being populated by the WofangQuery when eager-loading is set.
      error_info:
        description: 错误信息
        type: string
      id:
        description: ID of the ent.
        type: integer
      ip:
        description: IP
        type: string
      name:
        description: 名称
        type: string
      remark:
        description: 备注
        type: string
      start_time:
        description: 牵引清洗开始时间
        type: string
      status:
        description: 状态
        type: string
      tenant_id:
        description: 租户Id，可选
        type: integer
      type:
        description: 类型
        type: string
      un_drag_second:
        description: 自动解封时间，单位：秒
        type: integer
      updated_at:
        description: 更新时间
        type: string
    type: object
  ent.WofangAlert:
    properties:
      attack_id:
        description: AttackID holds the value of the "attack_id" field.
        type: integer
      attack_status:
        description: 攻击状态，1：攻击结束，2：攻击中
        type: integer
      attack_type:
        description: 攻击类型
        items:
          type: string
        type: array
      created_at:
        description: 创建时间
        type: string
      device_ip:
        description: DeviceIP holds the value of the "device_ip" field.
        type: string
      edges:
        allOf:
        - $ref: '#/definitions/ent.WofangAlertEdges'
        description: |-
          Edges holds the relations/edges for other nodes in the graph.
          The values are being populated by the WofangAlertQuery when eager-loading is set.
      end_time:
        description: 攻击结束时间
        type: string
      id:
        description: ID of the ent.
        type: integer
      max_drop_bps:
        description: MaxDropBps holds the value of the "max_drop_bps" field.
        type: integer
      max_in_bps:
        description: MaxInBps holds the value of the "max_in_bps" field.
        type: integer
      remark:
        description: 备注
        type: string
      start_time:
        description: 攻击开始时间
        type: string
      tenant_id:
        description: 租户Id，可选
        type: integer
      updated_at:
        description: 更新时间
        type: string
      zone_ip:
        description: 防护ip
        type: string
    type: object
  ent.WofangAlertEdges:
    properties:
      tenant:
        allOf:
        - $ref: '#/definitions/ent.Tenant'
        description: Tenant holds the value of the tenant edge.
    type: object
  ent.WofangEdges:
    properties:
      matrix_spectrum_alerts:
        description: MatrixSpectrumAlerts holds the value of the matrix_spectrum_alerts
          edge.
        items:
          $ref: '#/definitions/ent.MatrixSpectrumAlert'
        type: array
      spectrum_alerts:
        description: SpectrumAlerts holds the value of the spectrum_alerts edge.
        items:
          $ref: '#/definitions/ent.SpectrumAlert'
        type: array
      tenant:
        allOf:
        - $ref: '#/definitions/ent.Tenant'
        description: Tenant holds the value of the tenant edge.
      user:
        allOf:
        - $ref: '#/definitions/ent.User'
        description: User holds the value of the user edge.
    type: object
  gamecloud.AttackCounter:
    properties:
      Average:
        type: integer
      Max:
        type: integer
      "N":
        type: integer
      Name:
        type: string
      Sum:
        type: integer
      Unit:
        type: string
    type: object
  netease.AlertVO:
    properties:
      attackTypes:
        type: string
      endTime:
        type: string
      ip:
        type: string
      maxBps:
        type: integer
      maxPps:
        type: integer
      product:
        type: string
      startTime:
        type: string
    type: object
  netease.AttackInfo:
    properties:
      maxBps:
        description: 攻击最大bps
        type: integer
      maxCleanBps:
        description: 清洗的最大bps
        type: integer
      maxCleanPps:
        description: 清洗的最大pps
        type: integer
      maxPps:
        description: 攻击最大pps
        type: integer
    type: object
  netease.DragInfo:
    properties:
      autoDrag:
        description: 是否自动牵引
        type: boolean
      autoUnDrag:
        description: 是否自动回迁
        type: boolean
    type: object
  netease.GroupCleanData:
    properties:
      attackFlags:
        type: integer
      count:
        type: integer
      filter:
        type: string
      filterId:
        type: integer
      host:
        type: string
      inAckBps:
        type: integer
      inAckPps:
        type: integer
      inBps:
        type: integer
      inDnsBps:
        type: integer
      inDnsPps:
        type: integer
      inIcmpBps:
        type: integer
      inIcmpPps:
        type: integer
      inPps:
        type: integer
      inSynPps:
        type: integer
      inUdpBps:
        type: integer
      inUdpPps:
        type: integer
      ip:
        type: string
      ipType:
        type: integer
      outAckBps:
        type: integer
      outAckPps:
        type: integer
      outBps:
        type: integer
      outDnsBps:
        type: integer
      outDnsPps:
        type: integer
      outIcmpBps:
        type: integer
      outIcmpPps:
        type: integer
      outPps:
        type: integer
      outSynPps:
        type: integer
      outUdpBps:
        type: integer
      outUdpPps:
        type: integer
      time:
        type: integer
    type: object
  netease.GroupCleanDataResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/netease.GroupCleanData'
        type: array
      msg:
        type: string
    type: object
  netease.GroupSpectrumData:
    properties:
      ackBps:
        type: integer
      ackPps:
        type: integer
      bps:
        type: integer
      dataType:
        type: integer
      dnsAnswerBps:
        type: integer
      dnsAnswerPps:
        type: integer
      dnsQueryBps:
        type: integer
      dnsQueryPps:
        type: integer
      host:
        type: string
      icmpBps:
        type: integer
      icmpPps:
        type: integer
      ip:
        type: string
      ipType:
        type: integer
      monitor:
        type: string
      monitorId:
        type: integer
      ntpBps:
        type: integer
      ntpPps:
        type: integer
      pps:
        type: integer
      product:
        type: string
      qps:
        type: integer
      receviceCount:
        type: integer
      smallPps:
        type: integer
      ssdpBps:
        type: integer
      ssdpPps:
        type: integer
      synAckBps:
        type: integer
      synAckPps:
        type: integer
      synBps:
        type: integer
      synPps:
        type: integer
      time:
        type: integer
      udpBps:
        type: integer
      udpPps:
        type: integer
    type: object
  netease.GroupSpectrumDataResponse:
    properties:
      code:
        type: integer
      data:
        items:
          $ref: '#/definitions/netease.GroupSpectrumData'
        type: array
      msg:
        type: string
    type: object
  netease.GroupUpdate:
    properties:
      alertId:
        type: integer
      groupId:
        type: integer
      ips:
        items:
          type: string
        type: array
      type:
        type: integer
    type: object
  netease.MatrixAttackInfo:
    properties:
      lowerMonitorBpsCount:
        description: 小于监控bps阈值次数
        type: integer
      maxBps:
        description: 攻击最大bps
        type: integer
      projectID:
        type: integer
      spectrumAlertID:
        type: integer
    type: object
  netease.MonitorInfo:
    properties:
      ackBps:
        type: integer
      ackPps:
        type: integer
      autoML:
        description: 是否开启机器学习
        type: boolean
      icmpBps:
        type: integer
      icmpPps:
        type: integer
      synBps:
        type: integer
      synPps:
        type: integer
      totalBps:
        description: 单位bit/s
        type: integer
      totalPps:
        type: integer
      udpBps:
        type: integer
      udpPps:
        type: integer
    type: object
  netease.Nds4Config:
    properties:
      sAcl:
        description: ACL开关
        type: boolean
      sAntiOtherTcp:
        description: 防护除SYN外的TCP flood攻击  0-关 1-开 2-自动
        type: integer
      sBlacklist:
        description: 黑名单开关
        type: boolean
      sContentMatch:
        description: 特征匹配开关
        type: boolean
      sDstSpeedLimit:
        description: 目的ip限速开关
        type: boolean
      sEliminatePkt:
        description: 过滤特殊报文开关
        type: boolean
      sFirstPktDrop:
        description: 首包丢弃配置 ：0-关 1-开 2-自动
        type: integer
      sMalformedPkt:
        description: 畸形包报文检测开关
        type: boolean
      sPositionForeign:
        description: 国外地理位置：0-关 1-开 2-自动
        type: integer
      sPositionIdc:
        description: idc国家地理位置：0-关 1-开 2-自动
        type: integer
      sSourceCheck:
        description: 源认证配置 ：0-关 1-开 2-自动
        type: integer
      sSrcSpeedLimit:
        description: 源ip限速开关
        type: boolean
      sTcpReflection:
        description: TCP反射配置：0-关 1-开 2-自动
        type: integer
      sUdpReflection:
        description: UDP反射配置：0-关 1-开 2-自动
        type: integer
      sWhitelist:
        description: 白名单开关
        type: boolean
      tNoCleanLimit:
        description: 不清洗直接转发阈值
        type: integer
    type: object
  netease.Nds6Config:
    properties:
      sDstSpeedLimit:
        description: 目的ip限速开关
        type: boolean
      sMalformedPkt:
        description: 畸形包报文检测开关
        type: boolean
      sSrcSpeedLimit:
        description: 源ip限速开关
        type: boolean
      sUdpReflection:
        description: UDP反射配置：0-关 1-开 2-自动
        type: integer
      tNoCleanLimit:
        description: 不清洗直接转发阈值
        type: integer
    type: object
  netease.PushAlert:
    properties:
      alert_type:
        description: 1：NDS告警IP；2：机房出口告警IP
        type: integer
      bps:
        type: integer
      ip:
        type: string
      pps:
        type: integer
      product:
        type: string
      status:
        type: string
      time:
        type: integer
      type:
        type: string
    type: object
  socgroup.AddRequestData:
    properties:
      bandwidth:
        description: 牵引IP列表中最小的物理带宽或压测带宽(非业务流量)，保留两 位小数，单位Gbps
        type: number
      configArgs:
        description: 参数配置  仅[清洗上线和清洗调优且configType=1(自定义防护参数)] 有效
        type: string
      configType:
        description: 参数配置类型，0表示默认，1表示自定义  仅[清洗上线] 有效
        type: integer
      departmentId:
        description: 部⻔对应的ID，必须是SOC中已有的部⻔信息
        type: integer
      description:
        description: 工单描述信息
        type: string
      divertType:
        description: 清洗方式, 0表示清洗上线，1表示清洗下线 2调优
        type: integer
      emergencyContacterList:
        description: 工单应急接口人
        items:
          $ref: '#/definitions/socgroup.User'
        type: array
      followUserIdList:
        description: 工单跟踪者id列表
        items:
          type: integer
        type: array
      ipList:
        description: |-
          牵引IP列表
          如:
          *******-10
          *******/32
          ******* 一行一个，前两个代表IP段，最后一个代表单个IP
        type: string
      name:
        description: 标题
        type: string
      opTime:
        description: 操作时间，时间戳，毫秒
        type: integer
      opType:
        description: |-
          操作方式，0表示自动，1表示手动
          [清洗上线] 可以设置0，1
          [清洗下线、调优]只能设置1
        type: integer
      productAlias:
        description: 产品代号，不能包含中文(非必填)
        type: string
      productName:
        description: 产品中文名
        type: string
      workOrderType:
        description: DOSDIVERT固定值，表示DOS清洗防护工单
        type: string
    type: object
  socgroup.AddResponseData:
    properties:
      code:
        type: integer
      dataitems:
        $ref: '#/definitions/socgroup.WorkOrder'
      msg:
        type: string
    type: object
  socgroup.QueryResponseData:
    properties:
      code:
        description: 0表示成功，非0表示失败
        type: integer
      dataitems:
        description: 返回工单的内容
        items:
          $ref: '#/definitions/socgroup.WorkOrder'
        type: array
      msg:
        description: 返回的消息内容
        type: string
      total:
        description: 满足条件总数
        type: integer
    type: object
  socgroup.User:
    properties:
      email:
        type: string
      name:
        type: string
      phone:
        type: string
    type: object
  socgroup.WorkOrder:
    properties:
      applyUser:
        properties:
          count:
            type: integer
          createTime: {}
          department1: {}
          department2: {}
          department3: {}
          email:
            type: string
          fullname:
            type: string
          grantedAuthorityList:
            items: {}
            type: array
          id:
            type: integer
          lastLoginTimeDate: {}
          phone: {}
          supperUser:
            type: boolean
          userAuthorityList: {}
          userGroupList: {}
          userRole: {}
          username:
            type: string
        type: object
      assetsContacterGroup:
        properties:
          assetsType:
            type: string
          contacterGroupList:
            items:
              properties:
                baseContacterObject:
                  properties:
                    cclists:
                      items:
                        properties:
                          count:
                            type: integer
                          createTime: {}
                          department1: {}
                          department2: {}
                          department3: {}
                          email:
                            type: string
                          fullname:
                            type: string
                          grantedAuthorityList:
                            items: {}
                            type: array
                          id:
                            type: integer
                          lastLoginTimeDate: {}
                          phone: {}
                          supperUser:
                            type: boolean
                          userAuthorityList: {}
                          userGroupList: {}
                          userRole: {}
                          username:
                            type: string
                        type: object
                      type: array
                    contacters:
                      items:
                        properties:
                          count:
                            type: integer
                          createTime: {}
                          department1: {}
                          department2: {}
                          department3: {}
                          email:
                            type: string
                          fullname:
                            type: string
                          grantedAuthorityList:
                            items: {}
                            type: array
                          id:
                            type: integer
                          lastLoginTimeDate: {}
                          phone: {}
                          supperUser:
                            type: boolean
                          userAuthorityList: {}
                          userGroupList: {}
                          userRole: {}
                          username:
                            type: string
                        type: object
                      type: array
                    dataCategory:
                      properties:
                        description: {}
                        id:
                          type: integer
                        name:
                          type: string
                      type: object
                    departId: {}
                    departmentName: {}
                    entityEmpty:
                      type: boolean
                    leader: {}
                  type: object
                createTime:
                  type: integer
                description: {}
                entityEmpty:
                  type: boolean
                id:
                  type: integer
                mapGroupId:
                  type: integer
                name:
                  type: string
                notifyStrategy:
                  type: string
                remark: {}
                updateTime:
                  type: integer
              type: object
            type: array
          defaultContacterGroup:
            properties:
              baseContacterObject:
                properties:
                  cclists:
                    items:
                      properties:
                        count:
                          type: integer
                        createTime: {}
                        department1: {}
                        department2: {}
                        department3: {}
                        email:
                          type: string
                        fullname:
                          type: string
                        grantedAuthorityList:
                          items: {}
                          type: array
                        id:
                          type: integer
                        lastLoginTimeDate: {}
                        phone: {}
                        supperUser:
                          type: boolean
                        userAuthorityList: {}
                        userGroupList: {}
                        userRole: {}
                        username:
                          type: string
                      type: object
                    type: array
                  contacters:
                    items:
                      properties:
                        count:
                          type: integer
                        createTime: {}
                        department1: {}
                        department2: {}
                        department3: {}
                        email:
                          type: string
                        fullname:
                          type: string
                        grantedAuthorityList:
                          items: {}
                          type: array
                        id:
                          type: integer
                        lastLoginTimeDate: {}
                        phone: {}
                        supperUser:
                          type: boolean
                        userAuthorityList: {}
                        userGroupList: {}
                        userRole: {}
                        username:
                          type: string
                      type: object
                    type: array
                  dataCategory:
                    properties:
                      description: {}
                      id:
                        type: integer
                      name:
                        type: string
                    type: object
                  departId: {}
                  departmentName: {}
                  entityEmpty:
                    type: boolean
                  leader: {}
                type: object
              createTime:
                type: integer
              description: {}
              entityEmpty:
                type: boolean
              id:
                type: integer
              mapGroupId:
                type: integer
              name:
                type: string
              notifyStrategy:
                type: string
              remark: {}
              updateTime:
                type: integer
            type: object
          description: {}
          id:
            type: integer
          name: {}
          vulsContacterGroup: {}
        type: object
      assetsType:
        type: string
      attachFileName: {}
      attachGroupkey: {}
      autoTestType: {}
      commentStatus:
        type: integer
      createTime:
        description: 【创建时间】
        type: integer
      cycleStatus:
        type: integer
      department:
        properties:
          assetsContacterGroup: {}
          assetsType:
            type: string
          createTime:
            type: integer
          description:
            type: string
          id:
            type: integer
          leader: {}
          level:
            type: integer
          levelOneId:
            type: integer
          name:
            type: string
          oaId: {}
          parentOaId: {}
          remark: {}
          state: {}
          updateTime:
            type: integer
          upperName: {}
          upperid:
            type: integer
        type: object
      department1stName: {}
      department2ndName: {}
      department3rdName: {}
      departmentName: {}
      dept1: {}
      dept2: {}
      dept3: {}
      description:
        description: 【描述】
        type: string
      expectTime: {}
      extraConfig:
        properties:
          bandwidth:
            type: number
          configArgs:
            description: 【自定义的参数】
            type: string
          configType:
            description: 【参数配置类型】
            type: integer
          departmentId:
            description: 【SOC平台中的部⻔id】
            type: integer
          departmentName:
            type: string
          divertType:
            description: 【清洗方式】
            type: integer
          emergencyContacterList:
            description: 【紧急联系人】
            items:
              $ref: '#/definitions/socgroup.User'
            type: array
          ipList:
            description: 【IP列表】
            type: string
          opTime:
            description: 【操作时间】
            type: integer
          opType:
            description: 【操作方式】
            type: integer
          productAlias:
            description: 【自己填写的产品代号，选填，不可包含中文】
            type: string
          productName:
            description: 【自己填写的产品中文名】
            type: string
        type: object
      extraConfigString:
        description: 【忽略忽略忽略】
        type: string
      finalDeadlineTime: {}
      flowId: {}
      id:
        description: 【SOC上工单的ID】
        type: integer
      leader: {}
      name:
        description: 【标题】
        type: string
      parentId:
        type: integer
      phase:
        type: integer
      product: {}
      remark:
        description: 【备注】
      resultAttachFileName:
        type: string
      resultAttachGroupkey:
        type: string
      testCycle:
        type: integer
      updateTime:
        description: 【更新时间】
        type: integer
      url: {}
      workOrderStatus:
        type: string
      workOrderType:
        type: string
      workingDays:
        type: number
    type: object
  wofang.Add:
    properties:
      dragType:
        description: '类型，牵引: qy；黑洞：hd'
        type: string
      ip:
        type: string
      unDragSecond:
        description: 解封时长(秒)，表示多少秒后自动解封
        type: integer
    type: object
  wofang.Response:
    properties:
      data: {}
      msg:
        type: string
      status:
        type: integer
    type: object
info:
  contact:
    email: <EMAIL>
    name: API Support
  description: This is an auto-generated API Docs.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: API
  version: "0.1"
paths:
  /api/v1/alicloudoriginattackdata:
    get:
      consumes:
      - application/json
      description: Query 根据指定字段、时间范围查询或搜索 AliCloudOriginAttackData
      parameters:
      - description: created_at
        format: date-time
        in: query
        name: created_at
        type: string
      - description: updated_at
        format: date-time
        in: query
        name: updated_at
        type: string
      - description: remark
        in: query
        name: remark
        type: string
      - description: project
        in: query
        name: project
        type: string
      - description: cloud_type
        in: query
        name: cloud_type
        type: string
      - description: ip
        format: ipv4
        in: query
        name: ip
        type: string
      - description: start_time
        format: date-time
        in: query
        name: start_time
        type: string
      - description: end_time
        format: date-time
        in: query
        name: end_time
        type: string
      - description: duration
        in: query
        name: duration
        type: integer
      - description: pps
        in: query
        name: pps
        type: integer
      - description: mbps
        in: query
        name: mbps
        type: integer
      - description: status
        in: query
        name: status
        type: string
      - description: 需要搜索的值，多个值英文逗号,分隔
        in: query
        name: search
        type: string
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.AliCloudOriginAttackData'
                  type: array
              type: object
      summary: Query 根据指定字段、时间范围查询或搜索 AliCloudOriginAttackData
      tags:
      - AliCloudOriginAttackData
    post:
      consumes:
      - application/json
      description: Create 创建 AliCloudOriginAttackData
      parameters:
      - description: AliCloudOriginAttackData
        in: body
        name: alicloudoriginattackdata
        required: true
        schema:
          $ref: '#/definitions/ent.AliCloudOriginAttackData'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.AliCloudOriginAttackData'
              type: object
      summary: Create 创建 AliCloudOriginAttackData
      tags:
      - AliCloudOriginAttackData
  /api/v1/alicloudoriginattackdata/{id}:
    delete:
      consumes:
      - application/json
      description: DeleteByID 根据 ID 删除 AliCloudOriginAttackData
      parameters:
      - description: AliCloudOriginAttackData ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteByID 根据 ID 删除 AliCloudOriginAttackData
      tags:
      - AliCloudOriginAttackData
    get:
      consumes:
      - application/json
      description: QueryByID 根据 ID 查询 AliCloudOriginAttackData
      parameters:
      - description: AliCloudOriginAttackData ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.AliCloudOriginAttackData'
              type: object
      summary: QueryByID 根据 ID 查询 AliCloudOriginAttackData
      tags:
      - AliCloudOriginAttackData
    put:
      consumes:
      - application/json
      description: UpdateByID 根据 ID 修改 AliCloudOriginAttackData
      parameters:
      - description: AliCloudOriginAttackData ID
        in: path
        name: id
        required: true
        type: integer
      - description: AliCloudOriginAttackData
        in: body
        name: alicloudoriginattackdata
        required: true
        schema:
          $ref: '#/definitions/ent.AliCloudOriginAttackData'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.AliCloudOriginAttackData'
              type: object
      summary: UpdateByID 根据 ID 修改 AliCloudOriginAttackData
      tags:
      - AliCloudOriginAttackData
  /api/v1/alicloudoriginattackdata/bulk:
    post:
      consumes:
      - application/json
      description: CreateBulk 批量创建 AliCloudOriginAttackData
      parameters:
      - description: AliCloudOriginAttackData
        in: body
        name: alicloudoriginattackdata
        required: true
        schema:
          items:
            $ref: '#/definitions/ent.AliCloudOriginAttackData'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.AliCloudOriginAttackData'
                  type: array
              type: object
      summary: CreateBulk 批量创建 AliCloudOriginAttackData
      tags:
      - AliCloudOriginAttackData
  /api/v1/alicloudoriginattackdata/bulk/delete:
    post:
      consumes:
      - application/json
      description: DeleteBulk 根据 IDs 批量删除 AliCloudOriginAttackData
      parameters:
      - description: 需要删除的id列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/common.DeleteItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteBulk 根据 IDs 批量删除 AliCloudOriginAttackData
      tags:
      - AliCloudOriginAttackData
  /api/v1/casbinrule:
    get:
      consumes:
      - application/json
      description: Query 根据指定字段、时间范围查询或搜索 CasbinRule
      parameters:
      - description: NetType
        in: query
        name: NetType
        type: string
      - description: Sub
        in: query
        name: Sub
        type: string
      - description: Dom
        in: query
        name: Dom
        type: string
      - description: Obj
        in: query
        name: Obj
        type: string
      - description: Act
        in: query
        name: Act
        type: string
      - description: V4
        in: query
        name: V4
        type: string
      - description: V5
        in: query
        name: V5
        type: string
      - description: 需要搜索的值，多个值英文逗号,分隔
        in: query
        name: search
        type: string
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.CasbinRule'
                  type: array
              type: object
      summary: Query 根据指定字段、时间范围查询或搜索 CasbinRule
      tags:
      - CasbinRule
    post:
      consumes:
      - application/json
      description: Create 创建 CasbinRule
      parameters:
      - description: CasbinRule
        in: body
        name: casbinrule
        required: true
        schema:
          $ref: '#/definitions/ent.CasbinRule'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.CasbinRule'
              type: object
      summary: Create 创建 CasbinRule
      tags:
      - CasbinRule
  /api/v1/casbinrule/{id}:
    delete:
      consumes:
      - application/json
      description: DeleteByID 根据 ID 删除 CasbinRule
      parameters:
      - description: CasbinRule ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteByID 根据 ID 删除 CasbinRule
      tags:
      - CasbinRule
    get:
      consumes:
      - application/json
      description: QueryByID 根据 ID 查询 CasbinRule
      parameters:
      - description: CasbinRule ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.CasbinRule'
              type: object
      summary: QueryByID 根据 ID 查询 CasbinRule
      tags:
      - CasbinRule
    put:
      consumes:
      - application/json
      description: UpdateByID 根据 ID 修改 CasbinRule
      parameters:
      - description: CasbinRule ID
        in: path
        name: id
        required: true
        type: integer
      - description: CasbinRule
        in: body
        name: casbinrule
        required: true
        schema:
          $ref: '#/definitions/ent.CasbinRule'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.CasbinRule'
              type: object
      summary: UpdateByID 根据 ID 修改 CasbinRule
      tags:
      - CasbinRule
  /api/v1/casbinrule/bulk:
    post:
      consumes:
      - application/json
      description: CreateBulk 批量创建 CasbinRule
      parameters:
      - description: CasbinRule
        in: body
        name: casbinrule
        required: true
        schema:
          items:
            $ref: '#/definitions/ent.CasbinRule'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.CasbinRule'
                  type: array
              type: object
      summary: CreateBulk 批量创建 CasbinRule
      tags:
      - CasbinRule
  /api/v1/casbinrule/bulk/delete:
    post:
      consumes:
      - application/json
      description: DeleteBulk 根据 IDs 批量删除 CasbinRule
      parameters:
      - description: 需要删除的id列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/common.DeleteItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteBulk 根据 IDs 批量删除 CasbinRule
      tags:
      - CasbinRule
  /api/v1/chart/spectrum:
    get:
      consumes:
      - application/json
      description: Query 根据 IP和起始时间 查询 分光流量信息
      parameters:
      - description: ip
        format: ipv4
        in: query
        name: IP
        type: string
      - description: start_time
        format: date-time
        in: query
        name: startTime
        type: string
      - description: end_time
        format: date-time
        in: query
        name: endTime
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/chart.LineData'
                  type: array
              type: object
      summary: Query 根据 IP和起始时间 查询 分光流量信息
      tags:
      - Chart
  /api/v1/cleandata:
    get:
      consumes:
      - application/json
      description: Query 根据指定字段、时间范围查询或搜索 CleanData
      parameters:
      - description: ip
        format: ipv4
        in: query
        name: IP
        type: string
      - description: created_at
        format: date-time
        in: query
        name: created_at
        type: string
      - description: Time
        format: date-time
        in: query
        name: Time
        type: string
      - description: InBps
        in: query
        name: InBps
        type: integer
      - description: OutBps
        in: query
        name: OutBps
        type: integer
      - description: InPps
        in: query
        name: InPps
        type: integer
      - description: OutPps
        in: query
        name: OutPps
        type: integer
      - description: InAckPps
        in: query
        name: InAckPps
        type: integer
      - description: OutAckPps
        in: query
        name: OutAckPps
        type: integer
      - description: InAckBps
        in: query
        name: InAckBps
        type: integer
      - description: OutAckBps
        in: query
        name: OutAckBps
        type: integer
      - description: InSynPps
        in: query
        name: InSynPps
        type: integer
      - description: OutSynPps
        in: query
        name: OutSynPps
        type: integer
      - description: InUdpPps
        in: query
        name: InUdpPps
        type: integer
      - description: OutUdpPps
        in: query
        name: OutUdpPps
        type: integer
      - description: InUdpBps
        in: query
        name: InUdpBps
        type: integer
      - description: OutUdpBps
        in: query
        name: OutUdpBps
        type: integer
      - description: InIcmpPps
        in: query
        name: InIcmpPps
        type: integer
      - description: InIcmpBps
        in: query
        name: InIcmpBps
        type: integer
      - description: OutIcmpBps
        in: query
        name: OutIcmpBps
        type: integer
      - description: OutIcmpPps
        in: query
        name: OutIcmpPps
        type: integer
      - description: InDnsPps
        in: query
        name: InDnsPps
        type: integer
      - description: OutDnsPps
        in: query
        name: OutDnsPps
        type: integer
      - description: InDnsBps
        in: query
        name: InDnsBps
        type: integer
      - description: OutDnsBps
        in: query
        name: OutDnsBps
        type: integer
      - description: AttackFlags
        in: query
        name: AttackFlags
        type: integer
      - description: Count
        in: query
        name: Count
        type: integer
      - description: IpType
        in: query
        name: IpType
        type: integer
      - description: FFilter
        in: query
        name: FFilter
        type: string
      - description: Host
        in: query
        name: Host
        type: string
      - description: 需要搜索的值，多个值英文逗号,分隔
        in: query
        name: search
        type: string
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.CleanData'
                  type: array
              type: object
      summary: Query 根据指定字段、时间范围查询或搜索 CleanData
      tags:
      - CleanData
    post:
      consumes:
      - application/json
      description: Create 创建 CleanData
      parameters:
      - description: CleanData
        in: body
        name: cleandata
        required: true
        schema:
          $ref: '#/definitions/ent.CleanData'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.CleanData'
              type: object
      summary: Create 创建 CleanData
      tags:
      - CleanData
  /api/v1/cleandata/{id}:
    delete:
      consumes:
      - application/json
      description: DeleteByID 根据 ID 删除 CleanData
      parameters:
      - description: CleanData ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteByID 根据 ID 删除 CleanData
      tags:
      - CleanData
    get:
      consumes:
      - application/json
      description: QueryByID 根据 ID 查询 CleanData
      parameters:
      - description: CleanData ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.CleanData'
              type: object
      summary: QueryByID 根据 ID 查询 CleanData
      tags:
      - CleanData
    put:
      consumes:
      - application/json
      description: UpdateByID 根据 ID 修改 CleanData
      parameters:
      - description: CleanData ID
        in: path
        name: id
        required: true
        type: integer
      - description: CleanData
        in: body
        name: cleandata
        required: true
        schema:
          $ref: '#/definitions/ent.CleanData'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.CleanData'
              type: object
      summary: UpdateByID 根据 ID 修改 CleanData
      tags:
      - CleanData
  /api/v1/cleandata/bulk:
    post:
      consumes:
      - application/json
      description: CreateBulk 批量创建 CleanData
      parameters:
      - description: CleanData
        in: body
        name: cleandata
        required: true
        schema:
          items:
            $ref: '#/definitions/ent.CleanData'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.CleanData'
                  type: array
              type: object
      summary: CreateBulk 批量创建 CleanData
      tags:
      - CleanData
  /api/v1/cleandata/bulk/delete:
    post:
      consumes:
      - application/json
      description: DeleteBulk 根据 IDs 批量删除 CleanData
      parameters:
      - description: 需要删除的id列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/common.DeleteItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteBulk 根据 IDs 批量删除 CleanData
      tags:
      - CleanData
  /api/v1/cloudalert:
    get:
      consumes:
      - application/json
      description: Query 根据指定字段、时间范围查询或搜索 CloudAlert
      parameters:
      - description: created_at
        format: date-time
        in: query
        name: created_at
        type: string
      - description: updated_at
        format: date-time
        in: query
        name: updated_at
        type: string
      - description: remark
        in: query
        name: remark
        type: string
      - description: src_ip
        in: query
        name: src_ip
        type: string
      - description: src_port
        in: query
        name: src_port
        type: integer
      - description: dst_ip
        in: query
        name: dst_ip
        type: string
      - description: dst_port
        in: query
        name: dst_port
        type: integer
      - description: defence_mode
        in: query
        name: defence_mode
        type: integer
      - description: flow_mode
        in: query
        name: flow_mode
        type: integer
      - description: tcp_ack_num
        in: query
        name: tcp_ack_num
        type: string
      - description: tcp_seq_num
        in: query
        name: tcp_seq_num
        type: string
      - description: protocol
        in: query
        name: protocol
        type: integer
      - description: defence_level
        in: query
        name: defence_level
        type: string
      - description: start_time
        format: date-time
        in: query
        name: start_time
        type: string
      - description: end_time
        format: date-time
        in: query
        name: end_time
        type: string
      - description: 需要搜索的值，多个值英文逗号,分隔
        in: query
        name: search
        type: string
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.CloudAlert'
                  type: array
              type: object
      summary: Query 根据指定字段、时间范围查询或搜索 CloudAlert
      tags:
      - CloudAlert
    post:
      consumes:
      - application/json
      description: Create 创建 CloudAlert
      parameters:
      - description: CloudAlert
        in: body
        name: cloudalert
        required: true
        schema:
          $ref: '#/definitions/ent.CloudAlert'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.CloudAlert'
              type: object
      summary: Create 创建 CloudAlert
      tags:
      - CloudAlert
  /api/v1/cloudalert/{id}:
    delete:
      consumes:
      - application/json
      description: DeleteByID 根据 ID 删除 CloudAlert
      parameters:
      - description: CloudAlert ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteByID 根据 ID 删除 CloudAlert
      tags:
      - CloudAlert
    get:
      consumes:
      - application/json
      description: QueryByID 根据 ID 查询 CloudAlert
      parameters:
      - description: CloudAlert ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.CloudAlert'
              type: object
      summary: QueryByID 根据 ID 查询 CloudAlert
      tags:
      - CloudAlert
    put:
      consumes:
      - application/json
      description: UpdateByID 根据 ID 修改 CloudAlert
      parameters:
      - description: CloudAlert ID
        in: path
        name: id
        required: true
        type: integer
      - description: CloudAlert
        in: body
        name: cloudalert
        required: true
        schema:
          $ref: '#/definitions/ent.CloudAlert'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.CloudAlert'
              type: object
      summary: UpdateByID 根据 ID 修改 CloudAlert
      tags:
      - CloudAlert
  /api/v1/cloudalert/bulk:
    post:
      consumes:
      - application/json
      description: CreateBulk 批量创建 CloudAlert
      parameters:
      - description: CloudAlert
        in: body
        name: cloudalert
        required: true
        schema:
          items:
            $ref: '#/definitions/ent.CloudAlert'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.CloudAlert'
                  type: array
              type: object
      summary: CreateBulk 批量创建 CloudAlert
      tags:
      - CloudAlert
  /api/v1/cloudalert/bulk/delete:
    post:
      consumes:
      - application/json
      description: DeleteBulk 根据 IDs 批量删除 CloudAlert
      parameters:
      - description: 需要删除的id列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/common.DeleteItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteBulk 根据 IDs 批量删除 CloudAlert
      tags:
      - CloudAlert
  /api/v1/cloudattackdata:
    get:
      consumes:
      - application/json
      description: Query 根据指定字段、时间范围查询或搜索 CloudAttackData
      parameters:
      - description: created_at
        format: date-time
        in: query
        name: created_at
        type: string
      - description: updated_at
        format: date-time
        in: query
        name: updated_at
        type: string
      - description: remark
        in: query
        name: remark
        type: string
      - description: src_ip
        in: query
        name: src_ip
        type: string
      - description: src_port
        in: query
        name: src_port
        type: integer
      - description: dst_ip
        in: query
        name: dst_ip
        type: string
      - description: dst_port
        in: query
        name: dst_port
        type: integer
      - description: protocol
        in: query
        name: protocol
        type: integer
      - description: current_attack_pps
        in: query
        name: current_attack_pps
        type: integer
      - description: start_time
        format: date-time
        in: query
        name: start_time
        type: string
      - description: end_time
        format: date-time
        in: query
        name: end_time
        type: string
      - description: 需要搜索的值，多个值英文逗号,分隔
        in: query
        name: search
        type: string
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.CloudAttackData'
                  type: array
              type: object
      summary: Query 根据指定字段、时间范围查询或搜索 CloudAttackData
      tags:
      - CloudAttackData
    post:
      consumes:
      - application/json
      description: Create 创建 CloudAttackData
      parameters:
      - description: CloudAttackData
        in: body
        name: cloudattackdata
        required: true
        schema:
          $ref: '#/definitions/ent.CloudAttackData'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.CloudAttackData'
              type: object
      summary: Create 创建 CloudAttackData
      tags:
      - CloudAttackData
  /api/v1/cloudattackdata/{id}:
    delete:
      consumes:
      - application/json
      description: DeleteByID 根据 ID 删除 CloudAttackData
      parameters:
      - description: CloudAttackData ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteByID 根据 ID 删除 CloudAttackData
      tags:
      - CloudAttackData
    get:
      consumes:
      - application/json
      description: QueryByID 根据 ID 查询 CloudAttackData
      parameters:
      - description: CloudAttackData ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.CloudAttackData'
              type: object
      summary: QueryByID 根据 ID 查询 CloudAttackData
      tags:
      - CloudAttackData
    put:
      consumes:
      - application/json
      description: UpdateByID 根据 ID 修改 CloudAttackData
      parameters:
      - description: CloudAttackData ID
        in: path
        name: id
        required: true
        type: integer
      - description: CloudAttackData
        in: body
        name: cloudattackdata
        required: true
        schema:
          $ref: '#/definitions/ent.CloudAttackData'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.CloudAttackData'
              type: object
      summary: UpdateByID 根据 ID 修改 CloudAttackData
      tags:
      - CloudAttackData
  /api/v1/cloudattackdata/bulk:
    post:
      consumes:
      - application/json
      description: CreateBulk 批量创建 CloudAttackData
      parameters:
      - description: CloudAttackData
        in: body
        name: cloudattackdata
        required: true
        schema:
          items:
            $ref: '#/definitions/ent.CloudAttackData'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.CloudAttackData'
                  type: array
              type: object
      summary: CreateBulk 批量创建 CloudAttackData
      tags:
      - CloudAttackData
  /api/v1/cloudattackdata/bulk/delete:
    post:
      consumes:
      - application/json
      description: DeleteBulk 根据 IDs 批量删除 CloudAttackData
      parameters:
      - description: 需要删除的id列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/common.DeleteItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteBulk 根据 IDs 批量删除 CloudAttackData
      tags:
      - CloudAttackData
  /api/v1/cloudflowdata:
    get:
      consumes:
      - application/json
      description: Query 根据指定字段、时间范围查询或搜索 CloudFlowData
      parameters:
      - description: created_at
        format: date-time
        in: query
        name: created_at
        type: string
      - description: updated_at
        format: date-time
        in: query
        name: updated_at
        type: string
      - description: remark
        in: query
        name: remark
        type: string
      - description: src_ip
        in: query
        name: src_ip
        type: string
      - description: src_port
        in: query
        name: src_port
        type: integer
      - description: dst_ip
        in: query
        name: dst_ip
        type: string
      - description: dst_port
        in: query
        name: dst_port
        type: integer
      - description: protocol
        in: query
        name: protocol
        type: integer
      - description: count
        in: query
        name: count
        type: integer
      - description: start_time
        format: date-time
        in: query
        name: start_time
        type: string
      - description: end_time
        format: date-time
        in: query
        name: end_time
        type: string
      - description: 需要搜索的值，多个值英文逗号,分隔
        in: query
        name: search
        type: string
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.CloudFlowData'
                  type: array
              type: object
      summary: Query 根据指定字段、时间范围查询或搜索 CloudFlowData
      tags:
      - CloudFlowData
    post:
      consumes:
      - application/json
      description: Create 创建 CloudFlowData
      parameters:
      - description: CloudFlowData
        in: body
        name: cloudflowdata
        required: true
        schema:
          $ref: '#/definitions/ent.CloudFlowData'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.CloudFlowData'
              type: object
      summary: Create 创建 CloudFlowData
      tags:
      - CloudFlowData
  /api/v1/cloudflowdata/{id}:
    delete:
      consumes:
      - application/json
      description: DeleteByID 根据 ID 删除 CloudFlowData
      parameters:
      - description: CloudFlowData ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteByID 根据 ID 删除 CloudFlowData
      tags:
      - CloudFlowData
    get:
      consumes:
      - application/json
      description: QueryByID 根据 ID 查询 CloudFlowData
      parameters:
      - description: CloudFlowData ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.CloudFlowData'
              type: object
      summary: QueryByID 根据 ID 查询 CloudFlowData
      tags:
      - CloudFlowData
    put:
      consumes:
      - application/json
      description: UpdateByID 根据 ID 修改 CloudFlowData
      parameters:
      - description: CloudFlowData ID
        in: path
        name: id
        required: true
        type: integer
      - description: CloudFlowData
        in: body
        name: cloudflowdata
        required: true
        schema:
          $ref: '#/definitions/ent.CloudFlowData'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.CloudFlowData'
              type: object
      summary: UpdateByID 根据 ID 修改 CloudFlowData
      tags:
      - CloudFlowData
  /api/v1/cloudflowdata/bulk:
    post:
      consumes:
      - application/json
      description: CreateBulk 批量创建 CloudFlowData
      parameters:
      - description: CloudFlowData
        in: body
        name: cloudflowdata
        required: true
        schema:
          items:
            $ref: '#/definitions/ent.CloudFlowData'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.CloudFlowData'
                  type: array
              type: object
      summary: CreateBulk 批量创建 CloudFlowData
      tags:
      - CloudFlowData
  /api/v1/cloudflowdata/bulk/delete:
    post:
      consumes:
      - application/json
      description: DeleteBulk 根据 IDs 批量删除 CloudFlowData
      parameters:
      - description: 需要删除的id列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/common.DeleteItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteBulk 根据 IDs 批量删除 CloudFlowData
      tags:
      - CloudFlowData
  /api/v1/datasync:
    get:
      consumes:
      - application/json
      description: Query 根据指定字段、时间范围查询或搜索 DataSync
      parameters:
      - description: created_at
        format: date-time
        in: query
        name: created_at
        type: string
      - description: updated_at
        format: date-time
        in: query
        name: updated_at
        type: string
      - description: net_type
        in: query
        name: net_type
        type: string
      - description: type
        in: query
        name: type
        type: string
      - description: region
        in: query
        name: region
        type: string
      - description: source
        in: query
        name: source
        type: string
      - description: 需要搜索的值，多个值英文逗号,分隔
        in: query
        name: search
        type: string
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.DataSync'
                  type: array
              type: object
      summary: Query 根据指定字段、时间范围查询或搜索 DataSync
      tags:
      - DataSync
    post:
      consumes:
      - application/json
      description: Create 创建 DataSync
      parameters:
      - description: DataSync
        in: body
        name: datasync
        required: true
        schema:
          $ref: '#/definitions/ent.DataSync'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.DataSync'
              type: object
      summary: Create 创建 DataSync
      tags:
      - DataSync
  /api/v1/datasync/{id}:
    delete:
      consumes:
      - application/json
      description: DeleteByID 根据 ID 删除 DataSync
      parameters:
      - description: DataSync ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteByID 根据 ID 删除 DataSync
      tags:
      - DataSync
    get:
      consumes:
      - application/json
      description: QueryByID 根据 ID 查询 DataSync
      parameters:
      - description: DataSync ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.DataSync'
              type: object
      summary: QueryByID 根据 ID 查询 DataSync
      tags:
      - DataSync
    put:
      consumes:
      - application/json
      description: UpdateByID 根据 ID 修改 DataSync
      parameters:
      - description: DataSync ID
        in: path
        name: id
        required: true
        type: integer
      - description: DataSync
        in: body
        name: datasync
        required: true
        schema:
          $ref: '#/definitions/ent.DataSync'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.DataSync'
              type: object
      summary: UpdateByID 根据 ID 修改 DataSync
      tags:
      - DataSync
  /api/v1/datasync/bulk:
    post:
      consumes:
      - application/json
      description: CreateBulk 批量创建 DataSync
      parameters:
      - description: DataSync
        in: body
        name: datasync
        required: true
        schema:
          items:
            $ref: '#/definitions/ent.DataSync'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.DataSync'
                  type: array
              type: object
      summary: CreateBulk 批量创建 DataSync
      tags:
      - DataSync
  /api/v1/datasync/bulk/delete:
    post:
      consumes:
      - application/json
      description: DeleteBulk 根据 IDs 批量删除 DataSync
      parameters:
      - description: 需要删除的id列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/common.DeleteItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteBulk 根据 IDs 批量删除 DataSync
      tags:
      - DataSync
  /api/v1/group:
    get:
      consumes:
      - application/json
      description: Query 根据指定字段、时间范围查询或搜索 Group
      parameters:
      - description: Name
        in: query
        name: Name
        type: string
      - description: 需要搜索的值，多个值英文逗号,分隔
        in: query
        name: search
        type: string
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.ProtectGroup'
                  type: array
              type: object
      summary: Query 根据指定字段、时间范围查询或搜索 Group
      tags:
      - Group
    post:
      consumes:
      - application/json
      description: Create 创建 Group
      parameters:
      - description: Group
        in: body
        name: group
        required: true
        schema:
          $ref: '#/definitions/ent.ProtectGroup'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.ProtectGroup'
              type: object
      summary: Create 创建 Group
      tags:
      - Group
  /api/v1/group/{id}:
    delete:
      consumes:
      - application/json
      description: DeleteByID 根据 ID 删除 Group
      parameters:
      - description: Group ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteByID 根据 ID 删除 Group
      tags:
      - Group
    get:
      consumes:
      - application/json
      description: QueryByID 根据 ID 查询 Group
      parameters:
      - description: Group ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.ProtectGroup'
              type: object
      summary: QueryByID 根据 ID 查询 Group
      tags:
      - Group
    put:
      consumes:
      - application/json
      description: UpdateByID 根据 ID 修改 Group
      parameters:
      - description: Group ID
        in: path
        name: id
        required: true
        type: integer
      - description: Group
        in: body
        name: group
        required: true
        schema:
          $ref: '#/definitions/ent.ProtectGroup'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.ProtectGroup'
              type: object
      summary: UpdateByID 根据 ID 修改 Group
      tags:
      - Group
  /api/v1/group/bulk:
    post:
      consumes:
      - application/json
      description: CreateBulk 批量创建 Group
      parameters:
      - description: Group
        in: body
        name: group
        required: true
        schema:
          items:
            $ref: '#/definitions/ent.ProtectGroup'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.ProtectGroup'
                  type: array
              type: object
      summary: CreateBulk 批量创建 Group
      tags:
      - Group
  /api/v1/group/bulk/delete:
    post:
      consumes:
      - application/json
      description: DeleteBulk 根据 IDs 批量删除 Group
      parameters:
      - description: 需要删除的id列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/common.DeleteItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteBulk 根据 IDs 批量删除 Group
      tags:
      - Group
  /api/v1/matrixspectrumalert:
    get:
      consumes:
      - application/json
      description: Query 根据指定字段、时间范围查询或搜索 MatrixSpectrumAlert
      parameters:
      - description: created_at
        format: date-time
        in: query
        name: created_at
        type: string
      - description: updated_at
        format: date-time
        in: query
        name: updated_at
        type: string
      - description: remark
        in: query
        name: remark
        type: string
      - description: ip
        format: ipv4
        in: query
        name: ip
        type: string
      - description: device_name
        in: query
        name: device_name
        type: string
      - description: interface
        in: query
        name: interface
        type: string
      - description: protect_type
        in: query
        name: protect_type
        type: string
      - description: start_time
        format: date-time
        in: query
        name: start_time
        type: string
      - description: end_time
        format: date-time
        in: query
        name: end_time
        type: string
      - description: attack_type
        in: query
        name: attack_type
        type: string
      - description: bps
        in: query
        name: bps
        type: integer
      - description: 需要搜索的值，多个值英文逗号,分隔
        in: query
        name: search
        type: string
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.MatrixSpectrumAlert'
                  type: array
              type: object
      summary: Query 根据指定字段、时间范围查询或搜索 MatrixSpectrumAlert
      tags:
      - MatrixSpectrumAlert
    post:
      consumes:
      - application/json
      description: Create 创建 MatrixSpectrumAlert
      parameters:
      - description: MatrixSpectrumAlert
        in: body
        name: matrixspectrumalert
        required: true
        schema:
          $ref: '#/definitions/ent.MatrixSpectrumAlert'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.MatrixSpectrumAlert'
              type: object
      summary: Create 创建 MatrixSpectrumAlert
      tags:
      - MatrixSpectrumAlert
  /api/v1/matrixspectrumalert/{id}:
    delete:
      consumes:
      - application/json
      description: DeleteByID 根据 ID 删除 MatrixSpectrumAlert
      parameters:
      - description: MatrixSpectrumAlert ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteByID 根据 ID 删除 MatrixSpectrumAlert
      tags:
      - MatrixSpectrumAlert
    get:
      consumes:
      - application/json
      description: QueryByID 根据 ID 查询 MatrixSpectrumAlert
      parameters:
      - description: MatrixSpectrumAlert ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.MatrixSpectrumAlert'
              type: object
      summary: QueryByID 根据 ID 查询 MatrixSpectrumAlert
      tags:
      - MatrixSpectrumAlert
    put:
      consumes:
      - application/json
      description: UpdateByID 根据 ID 修改 MatrixSpectrumAlert
      parameters:
      - description: MatrixSpectrumAlert ID
        in: path
        name: id
        required: true
        type: integer
      - description: MatrixSpectrumAlert
        in: body
        name: matrixspectrumalert
        required: true
        schema:
          $ref: '#/definitions/ent.MatrixSpectrumAlert'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.MatrixSpectrumAlert'
              type: object
      summary: UpdateByID 根据 ID 修改 MatrixSpectrumAlert
      tags:
      - MatrixSpectrumAlert
  /api/v1/matrixspectrumalert/attacking:
    get:
      consumes:
      - application/json
      description: Query 获取攻击数据 MatrixSpectrumAlert
      parameters:
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.MatrixSpectrumAlert'
                  type: array
              type: object
      summary: Query 获取攻击数据 MatrixSpectrumAlert
      tags:
      - MatrixSpectrumAlert
  /api/v1/matrixspectrumalert/bulk:
    post:
      consumes:
      - application/json
      description: CreateBulk 批量创建 MatrixSpectrumAlert
      parameters:
      - description: MatrixSpectrumAlert
        in: body
        name: matrixspectrumalert
        required: true
        schema:
          items:
            $ref: '#/definitions/ent.MatrixSpectrumAlert'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.MatrixSpectrumAlert'
                  type: array
              type: object
      summary: CreateBulk 批量创建 MatrixSpectrumAlert
      tags:
      - MatrixSpectrumAlert
  /api/v1/matrixspectrumalert/bulk/delete:
    post:
      consumes:
      - application/json
      description: DeleteBulk 根据 IDs 批量删除 MatrixSpectrumAlert
      parameters:
      - description: 需要删除的id列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/common.DeleteItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteBulk 根据 IDs 批量删除 MatrixSpectrumAlert
      tags:
      - MatrixSpectrumAlert
  /api/v1/matrixspectrumdata:
    get:
      consumes:
      - application/json
      description: Query 根据指定字段、时间范围查询或搜索 MatrixSpectrumData
      parameters:
      - description: device_ip
        in: query
        name: device_ip
        type: string
      - description: device_name
        in: query
        name: device_name
        type: string
      - description: interface
        in: query
        name: interface
        type: string
      - description: bps
        in: query
        name: bps
        type: integer
      - description: time
        format: date-time
        in: query
        name: time
        type: string
      - description: 需要搜索的值，多个值英文逗号,分隔
        in: query
        name: search
        type: string
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.MatrixSpectrumData'
                  type: array
              type: object
      summary: Query 根据指定字段、时间范围查询或搜索 MatrixSpectrumData
      tags:
      - MatrixSpectrumData
    post:
      consumes:
      - application/json
      description: Create 创建 MatrixSpectrumData
      parameters:
      - description: MatrixSpectrumData
        in: body
        name: matrixspectrumdata
        required: true
        schema:
          $ref: '#/definitions/ent.MatrixSpectrumData'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.MatrixSpectrumData'
              type: object
      summary: Create 创建 MatrixSpectrumData
      tags:
      - MatrixSpectrumData
  /api/v1/matrixspectrumdata/{id}:
    delete:
      consumes:
      - application/json
      description: DeleteByID 根据 ID 删除 MatrixSpectrumData
      parameters:
      - description: MatrixSpectrumData ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteByID 根据 ID 删除 MatrixSpectrumData
      tags:
      - MatrixSpectrumData
    get:
      consumes:
      - application/json
      description: QueryByID 根据 ID 查询 MatrixSpectrumData
      parameters:
      - description: MatrixSpectrumData ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.MatrixSpectrumData'
              type: object
      summary: QueryByID 根据 ID 查询 MatrixSpectrumData
      tags:
      - MatrixSpectrumData
    put:
      consumes:
      - application/json
      description: UpdateByID 根据 ID 修改 MatrixSpectrumData
      parameters:
      - description: MatrixSpectrumData ID
        in: path
        name: id
        required: true
        type: integer
      - description: MatrixSpectrumData
        in: body
        name: matrixspectrumdata
        required: true
        schema:
          $ref: '#/definitions/ent.MatrixSpectrumData'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.MatrixSpectrumData'
              type: object
      summary: UpdateByID 根据 ID 修改 MatrixSpectrumData
      tags:
      - MatrixSpectrumData
  /api/v1/matrixspectrumdata/bulk:
    post:
      consumes:
      - application/json
      description: CreateBulk 批量创建 MatrixSpectrumData
      parameters:
      - description: MatrixSpectrumData
        in: body
        name: matrixspectrumdata
        required: true
        schema:
          items:
            $ref: '#/definitions/ent.MatrixSpectrumData'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.MatrixSpectrumData'
                  type: array
              type: object
      summary: CreateBulk 批量创建 MatrixSpectrumData
      tags:
      - MatrixSpectrumData
  /api/v1/matrixspectrumdata/bulk/delete:
    post:
      consumes:
      - application/json
      description: DeleteBulk 根据 IDs 批量删除 MatrixSpectrumData
      parameters:
      - description: 需要删除的id列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/common.DeleteItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteBulk 根据 IDs 批量删除 MatrixSpectrumData
      tags:
      - MatrixSpectrumData
  /api/v1/matrixstrategy:
    get:
      consumes:
      - application/json
      description: Query 根据指定字段、时间范围查询或搜索 MatrixStrategy
      parameters:
      - description: created_at
        format: date-time
        in: query
        name: created_at
        type: string
      - description: updated_at
        format: date-time
        in: query
        name: updated_at
        type: string
      - description: remark
        in: query
        name: remark
        type: string
      - description: name
        in: query
        name: name
        type: string
      - description: device
        in: query
        name: device
        type: string
      - description: monitor_bps
        in: query
        name: monitor_bps
        type: integer
      - description: drag_bps
        in: query
        name: drag_bps
        type: integer
      - description: drag_type
        in: query
        name: drag_type
        type: string
      - description: 需要搜索的值，多个值英文逗号,分隔
        in: query
        name: search
        type: string
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.MatrixStrategy'
                  type: array
              type: object
      summary: Query 根据指定字段、时间范围查询或搜索 MatrixStrategy
      tags:
      - MatrixStrategy
    post:
      consumes:
      - application/json
      description: Create 创建 MatrixStrategy
      parameters:
      - description: MatrixStrategy
        in: body
        name: matrixstrategy
        required: true
        schema:
          $ref: '#/definitions/ent.MatrixStrategy'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.MatrixStrategy'
              type: object
      summary: Create 创建 MatrixStrategy
      tags:
      - MatrixStrategy
  /api/v1/matrixstrategy/{id}:
    delete:
      consumes:
      - application/json
      description: DeleteByID 根据 ID 删除 MatrixStrategy
      parameters:
      - description: MatrixStrategy ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteByID 根据 ID 删除 MatrixStrategy
      tags:
      - MatrixStrategy
    get:
      consumes:
      - application/json
      description: QueryByID 根据 ID 查询 MatrixStrategy
      parameters:
      - description: MatrixStrategy ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.MatrixStrategy'
              type: object
      summary: QueryByID 根据 ID 查询 MatrixStrategy
      tags:
      - MatrixStrategy
    put:
      consumes:
      - application/json
      description: UpdateByID 根据 ID 修改 MatrixStrategy
      parameters:
      - description: MatrixStrategy ID
        in: path
        name: id
        required: true
        type: integer
      - description: MatrixStrategy
        in: body
        name: matrixstrategy
        required: true
        schema:
          $ref: '#/definitions/ent.MatrixStrategy'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.MatrixStrategy'
              type: object
      summary: UpdateByID 根据 ID 修改 MatrixStrategy
      tags:
      - MatrixStrategy
  /api/v1/matrixstrategy/bulk:
    post:
      consumes:
      - application/json
      description: CreateBulk 批量创建 MatrixStrategy
      parameters:
      - description: MatrixStrategy
        in: body
        name: matrixstrategy
        required: true
        schema:
          items:
            $ref: '#/definitions/ent.MatrixStrategy'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.MatrixStrategy'
                  type: array
              type: object
      summary: CreateBulk 批量创建 MatrixStrategy
      tags:
      - MatrixStrategy
  /api/v1/matrixstrategy/bulk/delete:
    post:
      consumes:
      - application/json
      description: DeleteBulk 根据 IDs 批量删除 MatrixStrategy
      parameters:
      - description: 需要删除的id列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/common.DeleteItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteBulk 根据 IDs 批量删除 MatrixStrategy
      tags:
      - MatrixStrategy
  /api/v1/nds/alert:
    get:
      consumes:
      - application/json
      description: 获取NDS时间范围内的告警数据
      parameters:
      - description: 开始时间
        format: date-time
        in: query
        name: startTime
        required: true
        type: string
      - description: 结束时间
        format: date-time
        in: query
        name: endTime
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/netease.AlertVO'
                  type: array
              type: object
      summary: 获取NDS时间范围内的告警数据
      tags:
      - NDS
  /api/v1/nds/cleandata:
    get:
      consumes:
      - application/json
      description: 获取NDS告警时间范围内的清洗数据
      parameters:
      - description: ip
        in: query
        name: ip
        required: true
        type: string
      - description: 开始时间
        format: date-time
        in: query
        name: startTime
        required: true
        type: string
      - description: 结束时间
        format: date-time
        in: query
        name: endTime
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/netease.GroupCleanDataResponse'
                  type: array
              type: object
      summary: 获取NDS告警时间范围内的清洗数据
      tags:
      - NDS
  /api/v1/nds/ip:
    post:
      consumes:
      - application/json
      description: 添加ip到NDS防护群组
      parameters:
      - description: GroupUpdate
        in: body
        name: GroupUpdate
        required: true
        schema:
          $ref: '#/definitions/netease.GroupUpdate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: 添加ip到NDS防护群组
      tags:
      - NDS
  /api/v1/nds/push:
    post:
      consumes:
      - application/json
      description: 集团NDS推送告警 PushAlert
      parameters:
      - description: PushAlert
        in: body
        name: PushAlert
        required: true
        schema:
          $ref: '#/definitions/netease.PushAlert'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: 集团NDS推送告警 PushAlert
      tags:
      - NDS
  /api/v1/nds/spectrumdata:
    get:
      consumes:
      - application/json
      description: 获取NDS告警时间范围内的分光数据
      parameters:
      - description: ip
        in: query
        name: ip
        required: true
        type: string
      - description: 开始时间
        format: date-time
        in: query
        name: startTime
        required: true
        type: string
      - description: 结束时间
        format: date-time
        in: query
        name: endTime
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/netease.GroupSpectrumDataResponse'
                  type: array
              type: object
      summary: 获取NDS告警时间范围内的分光数据
      tags:
      - NDS
  /api/v1/notify:
    get:
      consumes:
      - application/json
      description: Query 根据指定字段、时间范围查询或搜索 Notify
      parameters:
      - description: created_at
        format: date-time
        in: query
        name: created_at
        type: string
      - description: updated_at
        format: date-time
        in: query
        name: updated_at
        type: string
      - description: remark
        in: query
        name: remark
        type: string
      - description: name
        in: query
        name: name
        type: string
      - description: popo
        in: query
        name: popo
        type: boolean
      - description: email
        in: query
        name: email
        type: boolean
      - description: sms
        in: query
        name: sms
        type: boolean
      - description: phone
        in: query
        name: phone
        type: boolean
      - description: system
        in: query
        name: system
        type: boolean
      - description: enabled
        in: query
        name: enabled
        type: boolean
      - description: sa_notify_popo
        in: query
        name: sa_notify_popo
        type: boolean
      - description: sa_notify_email
        in: query
        name: sa_notify_email
        type: boolean
      - description: 需要搜索的值，多个值英文逗号,分隔
        in: query
        name: search
        type: string
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.Notify'
                  type: array
              type: object
      summary: Query 根据指定字段、时间范围查询或搜索 Notify
      tags:
      - Notify
    post:
      consumes:
      - application/json
      description: Create 创建 Notify
      parameters:
      - description: Notify
        in: body
        name: notify
        required: true
        schema:
          $ref: '#/definitions/ent.Notify'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.Notify'
              type: object
      summary: Create 创建 Notify
      tags:
      - Notify
  /api/v1/notify/{id}:
    delete:
      consumes:
      - application/json
      description: DeleteByID 根据 ID 删除 Notify
      parameters:
      - description: Notify ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteByID 根据 ID 删除 Notify
      tags:
      - Notify
    get:
      consumes:
      - application/json
      description: QueryByID 根据 ID 查询 Notify
      parameters:
      - description: Notify ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.Notify'
              type: object
      summary: QueryByID 根据 ID 查询 Notify
      tags:
      - Notify
    put:
      consumes:
      - application/json
      description: UpdateByID 根据 ID 修改 Notify
      parameters:
      - description: Notify ID
        in: path
        name: id
        required: true
        type: integer
      - description: Notify
        in: body
        name: notify
        required: true
        schema:
          $ref: '#/definitions/ent.Notify'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.Notify'
              type: object
      summary: UpdateByID 根据 ID 修改 Notify
      tags:
      - Notify
  /api/v1/notify/bulk:
    post:
      consumes:
      - application/json
      description: CreateBulk 批量创建 Notify
      parameters:
      - description: Notify
        in: body
        name: notify
        required: true
        schema:
          items:
            $ref: '#/definitions/ent.Notify'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.Notify'
                  type: array
              type: object
      summary: CreateBulk 批量创建 Notify
      tags:
      - Notify
  /api/v1/notify/bulk/delete:
    post:
      consumes:
      - application/json
      description: DeleteBulk 根据 IDs 批量删除 Notify
      parameters:
      - description: 需要删除的id列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/common.DeleteItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteBulk 根据 IDs 批量删除 Notify
      tags:
      - Notify
  /api/v1/protectgroup:
    get:
      consumes:
      - application/json
      description: Query 根据指定字段、时间范围查询或搜索 ProtectGroup
      parameters:
      - description: Remark
        in: query
        name: Remark
        type: string
      - description: created_at
        format: date-time
        in: query
        name: created_at
        type: string
      - description: updated_at
        format: date-time
        in: query
        name: updated_at
        type: string
      - description: GroupName
        in: query
        name: GroupName
        type: string
      - description: NetType
        in: query
        name: NetType
        type: integer
      - description: 需要搜索的值，多个值英文逗号,分隔
        in: query
        name: search
        type: string
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.ProtectGroup'
                  type: array
              type: object
      summary: Query 根据指定字段、时间范围查询或搜索 ProtectGroup
      tags:
      - ProtectGroup
    post:
      consumes:
      - application/json
      description: Create 创建 ProtectGroup
      parameters:
      - description: Group
        in: body
        name: protectgroup
        required: true
        schema:
          $ref: '#/definitions/ent.ProtectGroup'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.ProtectGroup'
              type: object
      summary: Create 创建 ProtectGroup
      tags:
      - ProtectGroup
  /api/v1/protectgroup/{id}:
    delete:
      consumes:
      - application/json
      description: DeleteByID 根据 ID 删除 ProtectGroup
      parameters:
      - description: Group ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteByID 根据 ID 删除 ProtectGroup
      tags:
      - ProtectGroup
    get:
      consumes:
      - application/json
      description: QueryByID 根据 ID 查询 ProtectGroup
      parameters:
      - description: Group ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.ProtectGroup'
              type: object
      summary: QueryByID 根据 ID 查询 ProtectGroup
      tags:
      - ProtectGroup
    put:
      consumes:
      - application/json
      description: UpdateByID 根据 ID 修改 ProtectGroup
      parameters:
      - description: Group ID
        in: path
        name: id
        required: true
        type: integer
      - description: Group
        in: body
        name: protectgroup
        required: true
        schema:
          $ref: '#/definitions/ent.ProtectGroup'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.ProtectGroup'
              type: object
      summary: UpdateByID 根据 ID 修改 ProtectGroup
      tags:
      - ProtectGroup
  /api/v1/protectgroup/bulk:
    post:
      consumes:
      - application/json
      description: CreateBulk 批量创建 ProtectGroup
      parameters:
      - description: Group
        in: body
        name: protectgroup
        required: true
        schema:
          items:
            $ref: '#/definitions/ent.ProtectGroup'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.ProtectGroup'
                  type: array
              type: object
      summary: CreateBulk 批量创建 ProtectGroup
      tags:
      - ProtectGroup
  /api/v1/protectgroup/bulk/delete:
    post:
      consumes:
      - application/json
      description: DeleteBulk 根据 IDs 批量删除 ProtectGroup
      parameters:
      - description: 需要删除的id列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/common.DeleteItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteBulk 根据 IDs 批量删除 ProtectGroup
      tags:
      - ProtectGroup
  /api/v1/skylinedos:
    get:
      consumes:
      - application/json
      description: Query 根据指定字段、时间范围查询或搜索 SkylineDos
      parameters:
      - description: created_at
        format: date-time
        in: query
        name: created_at
        type: string
      - description: updated_at
        format: date-time
        in: query
        name: updated_at
        type: string
      - description: remark
        in: query
        name: remark
        type: string
      - description: start_time
        format: date-time
        in: query
        name: start_time
        type: string
      - description: end_time
        format: date-time
        in: query
        name: end_time
        type: string
      - description: region
        in: query
        name: region
        type: string
      - description: resource
        in: query
        name: resource
        type: string
      - description: resource_type
        in: query
        name: resource_type
        type: string
      - description: status
        in: query
        name: status
        type: string
      - description: project
        in: query
        name: project
        type: string
      - description: duration_time
        in: query
        name: duration_time
        type: integer
      - description: 需要搜索的值，多个值英文逗号,分隔
        in: query
        name: search
        type: string
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.SkylineDos'
                  type: array
              type: object
      summary: Query 根据指定字段、时间范围查询或搜索 SkylineDos
      tags:
      - SkylineDos
    post:
      consumes:
      - application/json
      description: Create 创建 SkylineDos
      parameters:
      - description: SkylineDos
        in: body
        name: skylinedos
        required: true
        schema:
          $ref: '#/definitions/ent.SkylineDos'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.SkylineDos'
              type: object
      summary: Create 创建 SkylineDos
      tags:
      - SkylineDos
  /api/v1/skylinedos/{id}:
    delete:
      consumes:
      - application/json
      description: DeleteByID 根据 ID 删除 SkylineDos
      parameters:
      - description: SkylineDos ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteByID 根据 ID 删除 SkylineDos
      tags:
      - SkylineDos
    get:
      consumes:
      - application/json
      description: QueryByID 根据 ID 查询 SkylineDos
      parameters:
      - description: SkylineDos ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.SkylineDos'
              type: object
      summary: QueryByID 根据 ID 查询 SkylineDos
      tags:
      - SkylineDos
    put:
      consumes:
      - application/json
      description: UpdateByID 根据 ID 修改 SkylineDos
      parameters:
      - description: SkylineDos ID
        in: path
        name: id
        required: true
        type: integer
      - description: SkylineDos
        in: body
        name: skylinedos
        required: true
        schema:
          $ref: '#/definitions/ent.SkylineDos'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.SkylineDos'
              type: object
      summary: UpdateByID 根据 ID 修改 SkylineDos
      tags:
      - SkylineDos
  /api/v1/skylinedos/bulk:
    post:
      consumes:
      - application/json
      description: CreateBulk 批量创建 SkylineDos
      parameters:
      - description: SkylineDos
        in: body
        name: skylinedos
        required: true
        schema:
          items:
            $ref: '#/definitions/ent.SkylineDos'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.SkylineDos'
                  type: array
              type: object
      summary: CreateBulk 批量创建 SkylineDos
      tags:
      - SkylineDos
  /api/v1/skylinedos/bulk/delete:
    post:
      consumes:
      - application/json
      description: DeleteBulk 根据 IDs 批量删除 SkylineDos
      parameters:
      - description: 需要删除的id列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/common.DeleteItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteBulk 根据 IDs 批量删除 SkylineDos
      tags:
      - SkylineDos
  /api/v1/socgroup/ticket:
    get:
      consumes:
      - application/json
      description: Query 根据 起始时间和偏移值 查询 DDoS
      parameters:
      - description: startTime
        format: date-time
        in: query
        name: startTime
        required: true
        type: string
      - description: 偏移值
        in: query
        name: offset
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/socgroup.QueryResponseData'
                  - properties:
                      dataitems:
                        items:
                          $ref: '#/definitions/socgroup.WorkOrder'
                        type: array
                    type: object
              type: object
      summary: Query 根据 起始时间和偏移值 查询 DDoS
      tags:
      - SocGroup
    post:
      consumes:
      - application/json
      description: Add 提交 四层DDOS清洗防护 工单
      parameters:
      - description: Ticket
        in: body
        name: ticket
        required: true
        schema:
          $ref: '#/definitions/socgroup.AddRequestData'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  allOf:
                  - $ref: '#/definitions/socgroup.AddResponseData'
                  - properties:
                      dataitems:
                        $ref: '#/definitions/socgroup.WorkOrder'
                    type: object
              type: object
      summary: Add 提交 四层DDOS清洗防护 工单
      tags:
      - SocGroup
  /api/v1/socgroupticket:
    get:
      consumes:
      - application/json
      description: Query 根据指定字段、时间范围查询或搜索 SocGroupTicket
      parameters:
      - description: created_at
        format: date-time
        in: query
        name: created_at
        type: string
      - description: updated_at
        format: date-time
        in: query
        name: updated_at
        type: string
      - description: remark
        in: query
        name: remark
        type: string
      - description: name
        in: query
        name: name
        type: string
      - description: 需要搜索的值，多个值英文逗号,分隔
        in: query
        name: search
        type: string
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.SocGroupTicket'
                  type: array
              type: object
      summary: Query 根据指定字段、时间范围查询或搜索 SocGroupTicket
      tags:
      - SocGroupTicket
    post:
      consumes:
      - application/json
      description: Create 创建 SocGroupTicket
      parameters:
      - description: SocGroupTicket
        in: body
        name: socgroupticket
        required: true
        schema:
          $ref: '#/definitions/ent.SocGroupTicket'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.SocGroupTicket'
              type: object
      summary: Create 创建 SocGroupTicket
      tags:
      - SocGroupTicket
  /api/v1/socgroupticket/{id}:
    delete:
      consumes:
      - application/json
      description: DeleteByID 根据 ID 删除 SocGroupTicket
      parameters:
      - description: SocGroupTicket ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteByID 根据 ID 删除 SocGroupTicket
      tags:
      - SocGroupTicket
    get:
      consumes:
      - application/json
      description: QueryByID 根据 ID 查询 SocGroupTicket
      parameters:
      - description: SocGroupTicket ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.SocGroupTicket'
              type: object
      summary: QueryByID 根据 ID 查询 SocGroupTicket
      tags:
      - SocGroupTicket
    put:
      consumes:
      - application/json
      description: UpdateByID 根据 ID 修改 SocGroupTicket
      parameters:
      - description: SocGroupTicket ID
        in: path
        name: id
        required: true
        type: integer
      - description: SocGroupTicket
        in: body
        name: socgroupticket
        required: true
        schema:
          $ref: '#/definitions/ent.SocGroupTicket'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.SocGroupTicket'
              type: object
      summary: UpdateByID 根据 ID 修改 SocGroupTicket
      tags:
      - SocGroupTicket
  /api/v1/socgroupticket/bulk:
    post:
      consumes:
      - application/json
      description: CreateBulk 批量创建 SocGroupTicket
      parameters:
      - description: SocGroupTicket
        in: body
        name: socgroupticket
        required: true
        schema:
          items:
            $ref: '#/definitions/ent.SocGroupTicket'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.SocGroupTicket'
                  type: array
              type: object
      summary: CreateBulk 批量创建 SocGroupTicket
      tags:
      - SocGroupTicket
  /api/v1/socgroupticket/bulk/delete:
    post:
      consumes:
      - application/json
      description: DeleteBulk 根据 IDs 批量删除 SocGroupTicket
      parameters:
      - description: 需要删除的id列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/common.DeleteItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteBulk 根据 IDs 批量删除 SocGroupTicket
      tags:
      - SocGroupTicket
  /api/v1/spectrumalert:
    get:
      consumes:
      - application/json
      description: Query 根据指定字段、时间范围查询或搜索 SpectrumAlert
      parameters:
      - description: Remark
        in: query
        name: Remark
        type: string
      - description: created_at
        format: date-time
        in: query
        name: created_at
        type: string
      - description: updated_at
        format: date-time
        in: query
        name: updated_at
        type: string
      - description: IP
        format: ipv4
        in: query
        name: IP
        type: string
      - description: StartTime
        format: date-time
        in: query
        name: StartTime
        type: string
      - description: EndTime
        format: date-time
        in: query
        name: EndTime
        type: string
      - description: AttackType
        in: query
        name: AttackType
        type: string
      - description: Source
        in: query
        name: Source
        type: string
      - description: MaxPps
        in: query
        name: MaxPps
        type: string
      - description: MaxBps
        in: query
        name: MaxBps
        type: string
      - description: 需要搜索的值，多个值英文逗号,分隔
        in: query
        name: search
        type: string
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.SpectrumAlert'
                  type: array
              type: object
      summary: Query 根据指定字段、时间范围查询或搜索 SpectrumAlert
      tags:
      - SpectrumAlert
    post:
      consumes:
      - application/json
      description: Create 创建 SpectrumAlert
      parameters:
      - description: SpectrumAlert
        in: body
        name: spectrumalert
        required: true
        schema:
          $ref: '#/definitions/ent.SpectrumAlert'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.SpectrumAlert'
              type: object
      summary: Create 创建 SpectrumAlert
      tags:
      - SpectrumAlert
  /api/v1/spectrumalert/{id}:
    delete:
      consumes:
      - application/json
      description: DeleteByID 根据 ID 删除 SpectrumAlert
      parameters:
      - description: SpectrumAlert ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteByID 根据 ID 删除 SpectrumAlert
      tags:
      - SpectrumAlert
    get:
      consumes:
      - application/json
      description: QueryByID 根据 ID 查询 SpectrumAlert
      parameters:
      - description: SpectrumAlert ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.SpectrumAlert'
              type: object
      summary: QueryByID 根据 ID 查询 SpectrumAlert
      tags:
      - SpectrumAlert
    put:
      consumes:
      - application/json
      description: UpdateByID 根据 ID 修改 SpectrumAlert
      parameters:
      - description: SpectrumAlert ID
        in: path
        name: id
        required: true
        type: integer
      - description: SpectrumAlert
        in: body
        name: spectrumalert
        required: true
        schema:
          $ref: '#/definitions/ent.SpectrumAlert'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.SpectrumAlert'
              type: object
      summary: UpdateByID 根据 ID 修改 SpectrumAlert
      tags:
      - SpectrumAlert
  /api/v1/spectrumalert/attacking:
    get:
      consumes:
      - application/json
      description: Query 获取攻击数据 SpectrumAlert
      parameters:
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.SpectrumAlert'
                  type: array
              type: object
      summary: Query 获取攻击数据 SpectrumAlert
      tags:
      - SpectrumAlert
  /api/v1/spectrumalert/bulk:
    post:
      consumes:
      - application/json
      description: CreateBulk 批量创建 SpectrumAlert
      parameters:
      - description: SpectrumAlert
        in: body
        name: spectrumalert
        required: true
        schema:
          items:
            $ref: '#/definitions/ent.SpectrumAlert'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.SpectrumAlert'
                  type: array
              type: object
      summary: CreateBulk 批量创建 SpectrumAlert
      tags:
      - SpectrumAlert
  /api/v1/spectrumalert/bulk/delete:
    post:
      consumes:
      - application/json
      description: DeleteBulk 根据 IDs 批量删除 SpectrumAlert
      parameters:
      - description: 需要删除的id列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/common.DeleteItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteBulk 根据 IDs 批量删除 SpectrumAlert
      tags:
      - SpectrumAlert
  /api/v1/spectrumdata:
    get:
      consumes:
      - application/json
      description: Query 根据指定字段、时间范围查询或搜索 SpectrumData
      parameters:
      - description: IP
        format: ipv4
        in: query
        name: IP
        type: string
      - description: created_at
        format: date-time
        in: query
        name: created_at
        type: string
      - description: Time
        format: date-time
        in: query
        name: Time
        type: string
      - description: DataType
        in: query
        name: DataType
        type: integer
      - description: MatrixBps
        in: query
        name: MatrixBps
        type: integer
      - description: MatrixPps
        in: query
        name: MatrixPps
        type: integer
      - description: SynBps
        in: query
        name: SynBps
        type: integer
      - description: SynPps
        in: query
        name: SynPps
        type: integer
      - description: AckBps
        in: query
        name: AckBps
        type: integer
      - description: AckPps
        in: query
        name: AckPps
        type: integer
      - description: SynAckBps
        in: query
        name: SynAckBps
        type: integer
      - description: SynAckPps
        in: query
        name: SynAckPps
        type: integer
      - description: IcmpBps
        in: query
        name: IcmpBps
        type: integer
      - description: IcmpPps
        in: query
        name: IcmpPps
        type: integer
      - description: SmallPps
        in: query
        name: SmallPps
        type: integer
      - description: NtpPps
        in: query
        name: NtpPps
        type: integer
      - description: NtpBps
        in: query
        name: NtpBps
        type: integer
      - description: DnsQueryPps
        in: query
        name: DnsQueryPps
        type: integer
      - description: DnsQueryBps
        in: query
        name: DnsQueryBps
        type: integer
      - description: DnsAnswerPps
        in: query
        name: DnsAnswerPps
        type: integer
      - description: DnsAnswerBps
        in: query
        name: DnsAnswerBps
        type: integer
      - description: SsdpBps
        in: query
        name: SsdpBps
        type: integer
      - description: SsdpPps
        in: query
        name: SsdpPps
        type: integer
      - description: UdpPps
        in: query
        name: UdpPps
        type: integer
      - description: UdpBps
        in: query
        name: UdpBps
        type: integer
      - description: QPS
        in: query
        name: QPS
        type: integer
      - description: ReceiveCount
        in: query
        name: ReceiveCount
        type: integer
      - description: IpType
        in: query
        name: IpType
        type: integer
      - description: Monitor
        in: query
        name: Monitor
        type: string
      - description: Product
        in: query
        name: Product
        type: string
      - description: Host
        in: query
        name: Host
        type: string
      - description: 需要搜索的值，多个值英文逗号,分隔
        in: query
        name: search
        type: string
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.SpectrumData'
                  type: array
              type: object
      summary: Query 根据指定字段、时间范围查询或搜索 SpectrumData
      tags:
      - SpectrumData
    post:
      consumes:
      - application/json
      description: Create 创建 SpectrumData
      parameters:
      - description: SpectrumData
        in: body
        name: spectrumdata
        required: true
        schema:
          $ref: '#/definitions/ent.SpectrumData'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.SpectrumData'
              type: object
      summary: Create 创建 SpectrumData
      tags:
      - SpectrumData
  /api/v1/spectrumdata/{id}:
    delete:
      consumes:
      - application/json
      description: DeleteByID 根据 ID 删除 SpectrumData
      parameters:
      - description: SpectrumData ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteByID 根据 ID 删除 SpectrumData
      tags:
      - SpectrumData
    get:
      consumes:
      - application/json
      description: QueryByID 根据 ID 查询 SpectrumData
      parameters:
      - description: SpectrumData ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.SpectrumData'
              type: object
      summary: QueryByID 根据 ID 查询 SpectrumData
      tags:
      - SpectrumData
    put:
      consumes:
      - application/json
      description: UpdateByID 根据 ID 修改 SpectrumData
      parameters:
      - description: SpectrumData ID
        in: path
        name: id
        required: true
        type: integer
      - description: SpectrumData
        in: body
        name: spectrumdata
        required: true
        schema:
          $ref: '#/definitions/ent.SpectrumData'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.SpectrumData'
              type: object
      summary: UpdateByID 根据 ID 修改 SpectrumData
      tags:
      - SpectrumData
  /api/v1/spectrumdata/bulk:
    post:
      consumes:
      - application/json
      description: CreateBulk 批量创建 SpectrumData
      parameters:
      - description: SpectrumData
        in: body
        name: spectrumdata
        required: true
        schema:
          items:
            $ref: '#/definitions/ent.SpectrumData'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.SpectrumData'
                  type: array
              type: object
      summary: CreateBulk 批量创建 SpectrumData
      tags:
      - SpectrumData
  /api/v1/spectrumdata/bulk/delete:
    post:
      consumes:
      - application/json
      description: DeleteBulk 根据 IDs 批量删除 SpectrumData
      parameters:
      - description: 需要删除的id列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/common.DeleteItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteBulk 根据 IDs 批量删除 SpectrumData
      tags:
      - SpectrumData
  /api/v1/strategy:
    get:
      consumes:
      - application/json
      description: Query 根据指定字段、时间范围查询或搜索 Strategy
      parameters:
      - description: Remark
        in: query
        name: Remark
        type: string
      - description: created_at
        format: date-time
        in: query
        name: created_at
        type: string
      - description: updated_at
        format: date-time
        in: query
        name: updated_at
        type: string
      - description: Name
        in: query
        name: Name
        type: string
      - description: NetType
        in: query
        name: NetType
        type: string
      - description: Base
        in: query
        name: Base
        type: boolean
      - description: MatrixBps
        in: query
        name: MatrixBps
        type: integer
      - description: MatrixPps
        in: query
        name: MatrixPps
        type: integer
      - description: BpsCount
        in: query
        name: BpsCount
        type: integer
      - description: PpsCount
        in: query
        name: PpsCount
        type: integer
      - description: 需要搜索的值，多个值英文逗号,分隔
        in: query
        name: search
        type: string
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.Strategy'
                  type: array
              type: object
      summary: Query 根据指定字段、时间范围查询或搜索 Strategy
      tags:
      - Strategy
    post:
      consumes:
      - application/json
      description: Create 创建 Strategy
      parameters:
      - description: Strategy
        in: body
        name: strategy
        required: true
        schema:
          $ref: '#/definitions/ent.Strategy'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.Strategy'
              type: object
      summary: Create 创建 Strategy
      tags:
      - Strategy
  /api/v1/strategy/{id}:
    delete:
      consumes:
      - application/json
      description: DeleteByID 根据 ID 删除 Strategy
      parameters:
      - description: Strategy ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteByID 根据 ID 删除 Strategy
      tags:
      - Strategy
    get:
      consumes:
      - application/json
      description: QueryByID 根据 ID 查询 Strategy
      parameters:
      - description: Strategy ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.Strategy'
              type: object
      summary: QueryByID 根据 ID 查询 Strategy
      tags:
      - Strategy
    put:
      consumes:
      - application/json
      description: UpdateByID 根据 ID 修改 Strategy
      parameters:
      - description: Strategy ID
        in: path
        name: id
        required: true
        type: integer
      - description: Strategy
        in: body
        name: strategy
        required: true
        schema:
          $ref: '#/definitions/ent.Strategy'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.Strategy'
              type: object
      summary: UpdateByID 根据 ID 修改 Strategy
      tags:
      - Strategy
  /api/v1/strategy/bulk:
    post:
      consumes:
      - application/json
      description: CreateBulk 批量创建 Strategy
      parameters:
      - description: Strategy
        in: body
        name: strategy
        required: true
        schema:
          items:
            $ref: '#/definitions/ent.Strategy'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.Strategy'
                  type: array
              type: object
      summary: CreateBulk 批量创建 Strategy
      tags:
      - Strategy
  /api/v1/strategy/bulk/delete:
    post:
      consumes:
      - application/json
      description: DeleteBulk 根据 IDs 批量删除 Strategy
      parameters:
      - description: 需要删除的id列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/common.DeleteItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteBulk 根据 IDs 批量删除 Strategy
      tags:
      - Strategy
  /api/v1/systemapi:
    get:
      consumes:
      - application/json
      description: Query 根据指定字段、时间范围查询或搜索 SystemApi
      parameters:
      - description: created_at
        format: date-time
        in: query
        name: created_at
        type: string
      - description: updated_at
        format: date-time
        in: query
        name: updated_at
        type: string
      - description: remark
        in: query
        name: remark
        type: string
      - description: name
        in: query
        name: name
        type: string
      - description: path
        in: query
        name: path
        type: string
      - description: http_method
        in: query
        name: http_method
        type: string
      - description: public
        in: query
        name: public
        type: boolean
      - description: 需要搜索的值，多个值英文逗号,分隔
        in: query
        name: search
        type: string
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.SystemApi'
                  type: array
              type: object
      summary: Query 根据指定字段、时间范围查询或搜索 SystemApi
      tags:
      - SystemApi
    post:
      consumes:
      - application/json
      description: Create 创建 SystemApi
      parameters:
      - description: SystemApi
        in: body
        name: systemapi
        required: true
        schema:
          $ref: '#/definitions/ent.SystemApi'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.SystemApi'
              type: object
      summary: Create 创建 SystemApi
      tags:
      - SystemApi
  /api/v1/systemapi/{id}:
    delete:
      consumes:
      - application/json
      description: DeleteByID 根据 ID 删除 SystemApi
      parameters:
      - description: SystemApi ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteByID 根据 ID 删除 SystemApi
      tags:
      - SystemApi
    get:
      consumes:
      - application/json
      description: QueryByID 根据 ID 查询 SystemApi
      parameters:
      - description: SystemApi ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.SystemApi'
              type: object
      summary: QueryByID 根据 ID 查询 SystemApi
      tags:
      - SystemApi
    put:
      consumes:
      - application/json
      description: UpdateByID 根据 ID 修改 SystemApi
      parameters:
      - description: SystemApi ID
        in: path
        name: id
        required: true
        type: integer
      - description: SystemApi
        in: body
        name: systemapi
        required: true
        schema:
          $ref: '#/definitions/ent.SystemApi'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.SystemApi'
              type: object
      summary: UpdateByID 根据 ID 修改 SystemApi
      tags:
      - SystemApi
  /api/v1/systemapi/bulk:
    post:
      consumes:
      - application/json
      description: CreateBulk 批量创建 SystemApi
      parameters:
      - description: SystemApi
        in: body
        name: systemapi
        required: true
        schema:
          items:
            $ref: '#/definitions/ent.SystemApi'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.SystemApi'
                  type: array
              type: object
      summary: CreateBulk 批量创建 SystemApi
      tags:
      - SystemApi
  /api/v1/systemapi/bulk/delete:
    post:
      consumes:
      - application/json
      description: DeleteBulk 根据 IDs 批量删除 SystemApi
      parameters:
      - description: 需要删除的id列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/common.DeleteItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteBulk 根据 IDs 批量删除 SystemApi
      tags:
      - SystemApi
  /api/v1/systemconfig:
    get:
      consumes:
      - application/json
      description: Query 根据指定字段、时间范围查询或搜索 SystemConfig
      parameters:
      - description: created_at
        format: date-time
        in: query
        name: created_at
        type: string
      - description: updated_at
        format: date-time
        in: query
        name: updated_at
        type: string
      - description: remark
        in: query
        name: remark
        type: string
      - description: wofang_test_ip
        in: query
        name: wofang_test_ip
        type: string
      - description: 需要搜索的值，多个值英文逗号,分隔
        in: query
        name: search
        type: string
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.SystemConfig'
                  type: array
              type: object
      summary: Query 根据指定字段、时间范围查询或搜索 SystemConfig
      tags:
      - SystemConfig
    post:
      consumes:
      - application/json
      description: Create 创建 SystemConfig
      parameters:
      - description: SystemConfig
        in: body
        name: systemconfig
        required: true
        schema:
          $ref: '#/definitions/ent.SystemConfig'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.SystemConfig'
              type: object
      summary: Create 创建 SystemConfig
      tags:
      - SystemConfig
  /api/v1/systemconfig/{id}:
    delete:
      consumes:
      - application/json
      description: DeleteByID 根据 ID 删除 SystemConfig
      parameters:
      - description: SystemConfig ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteByID 根据 ID 删除 SystemConfig
      tags:
      - SystemConfig
    get:
      consumes:
      - application/json
      description: QueryByID 根据 ID 查询 SystemConfig
      parameters:
      - description: SystemConfig ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.SystemConfig'
              type: object
      summary: QueryByID 根据 ID 查询 SystemConfig
      tags:
      - SystemConfig
    put:
      consumes:
      - application/json
      description: UpdateByID 根据 ID 修改 SystemConfig
      parameters:
      - description: SystemConfig ID
        in: path
        name: id
        required: true
        type: integer
      - description: SystemConfig
        in: body
        name: systemconfig
        required: true
        schema:
          $ref: '#/definitions/ent.SystemConfig'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.SystemConfig'
              type: object
      summary: UpdateByID 根据 ID 修改 SystemConfig
      tags:
      - SystemConfig
  /api/v1/systemconfig/bulk:
    post:
      consumes:
      - application/json
      description: CreateBulk 批量创建 SystemConfig
      parameters:
      - description: SystemConfig
        in: body
        name: systemconfig
        required: true
        schema:
          items:
            $ref: '#/definitions/ent.SystemConfig'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.SystemConfig'
                  type: array
              type: object
      summary: CreateBulk 批量创建 SystemConfig
      tags:
      - SystemConfig
  /api/v1/systemconfig/bulk/delete:
    post:
      consumes:
      - application/json
      description: DeleteBulk 根据 IDs 批量删除 SystemConfig
      parameters:
      - description: 需要删除的id列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/common.DeleteItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteBulk 根据 IDs 批量删除 SystemConfig
      tags:
      - SystemConfig
  /api/v1/tenant:
    get:
      consumes:
      - application/json
      description: Query 根据指定字段、时间范围查询或搜索 Tenant
      parameters:
      - description: Name
        in: query
        name: Name
        type: string
      - description: 需要搜索的值，多个值英文逗号,分隔
        in: query
        name: search
        type: string
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.Tenant'
                  type: array
              type: object
      summary: Query 根据指定字段、时间范围查询或搜索 Tenant
      tags:
      - Tenant
    post:
      consumes:
      - application/json
      description: Create 创建 Tenant
      parameters:
      - description: Tenant
        in: body
        name: tenant
        required: true
        schema:
          $ref: '#/definitions/ent.Tenant'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.Tenant'
              type: object
      summary: Create 创建 Tenant
      tags:
      - Tenant
  /api/v1/tenant/{id}:
    delete:
      consumes:
      - application/json
      description: DeleteByID 根据 ID 删除 Tenant
      parameters:
      - description: Tenant ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteByID 根据 ID 删除 Tenant
      tags:
      - Tenant
    get:
      consumes:
      - application/json
      description: QueryByID 根据 ID 查询 Tenant
      parameters:
      - description: Tenant ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.Tenant'
              type: object
      summary: QueryByID 根据 ID 查询 Tenant
      tags:
      - Tenant
    put:
      consumes:
      - application/json
      description: UpdateByID 根据 ID 修改 Tenant
      parameters:
      - description: Tenant ID
        in: path
        name: id
        required: true
        type: integer
      - description: Tenant
        in: body
        name: tenant
        required: true
        schema:
          $ref: '#/definitions/ent.Tenant'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.Tenant'
              type: object
      summary: UpdateByID 根据 ID 修改 Tenant
      tags:
      - Tenant
  /api/v1/tenant/bulk:
    post:
      consumes:
      - application/json
      description: CreateBulk 批量创建 Tenant
      parameters:
      - description: Tenant
        in: body
        name: tenant
        required: true
        schema:
          items:
            $ref: '#/definitions/ent.Tenant'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.Tenant'
                  type: array
              type: object
      summary: CreateBulk 批量创建 Tenant
      tags:
      - Tenant
  /api/v1/tenant/bulk/delete:
    post:
      consumes:
      - application/json
      description: DeleteBulk 根据 IDs 批量删除 Tenant
      parameters:
      - description: 需要删除的id列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/common.DeleteItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteBulk 根据 IDs 批量删除 Tenant
      tags:
      - Tenant
  /api/v1/user:
    get:
      consumes:
      - application/json
      description: Query 根据指定字段、时间范围查询或搜索 User
      parameters:
      - description: Name
        in: query
        name: Name
        type: string
      - description: created_at
        format: date-time
        in: query
        name: created_at
        type: string
      - description: updated_at
        format: date-time
        in: query
        name: updated_at
        type: string
      - description: Password
        format: password
        in: query
        name: Password
        type: string
      - description: SuperAdmin
        in: query
        name: SuperAdmin
        type: boolean
      - description: 需要搜索的值，多个值英文逗号,分隔
        in: query
        name: search
        type: string
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.User'
                  type: array
              type: object
      summary: Query 根据指定字段、时间范围查询或搜索 User
      tags:
      - User
    post:
      consumes:
      - application/json
      description: Create 创建 User
      parameters:
      - description: User
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/ent.User'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.User'
              type: object
      summary: Create 创建 User
      tags:
      - User
  /api/v1/user/{id}:
    delete:
      consumes:
      - application/json
      description: DeleteByID 根据 ID 删除 User
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteByID 根据 ID 删除 User
      tags:
      - User
    get:
      consumes:
      - application/json
      description: QueryByID 根据 ID 查询 User
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.User'
              type: object
      summary: QueryByID 根据 ID 查询 User
      tags:
      - User
    put:
      consumes:
      - application/json
      description: UpdateByID 根据 ID 修改 User
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      - description: User
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/ent.User'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.User'
              type: object
      summary: UpdateByID 根据 ID 修改 User
      tags:
      - User
  /api/v1/user/bulk:
    post:
      consumes:
      - application/json
      description: CreateBulk 批量创建 User
      parameters:
      - description: User
        in: body
        name: user
        required: true
        schema:
          items:
            $ref: '#/definitions/ent.User'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.User'
                  type: array
              type: object
      summary: CreateBulk 批量创建 User
      tags:
      - User
  /api/v1/user/bulk/delete:
    post:
      consumes:
      - application/json
      description: DeleteBulk 根据 IDs 批量删除 User
      parameters:
      - description: 需要删除的id列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/common.DeleteItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteBulk 根据 IDs 批量删除 User
      tags:
      - User
  /api/v1/user/info:
    get:
      consumes:
      - application/json
      description: UserInfo 获取当前用户信息 User
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  type: string
              type: object
      summary: UserInfo 获取当前用户信息 User
      tags:
      - User
  /api/v1/user/login:
    post:
      consumes:
      - application/json
      description: Login 根据用户名和密码登录 User
      parameters:
      - description: User
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/ent.User'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  type: string
              type: object
      summary: Login 根据用户名和密码登录 User
      tags:
      - User
  /api/v1/user/logout:
    get:
      consumes:
      - application/json
      description: Logout 根据退出登录 User
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: Logout 根据退出登录 User
      tags:
      - User
  /api/v1/useroperationlog:
    get:
      consumes:
      - application/json
      description: Query 根据指定字段、时间范围查询或搜索 UserOperationLog
      parameters:
      - description: created_at
        format: date-time
        in: query
        name: created_at
        type: string
      - description: updated_at
        format: date-time
        in: query
        name: updated_at
        type: string
      - description: username
        in: query
        name: username
        type: string
      - description: method
        in: query
        name: method
        type: string
      - description: uri
        in: query
        name: uri
        type: string
      - description: request_body
        in: query
        name: request_body
        type: string
      - description: project
        in: query
        name: project
        type: string
      - description: time
        format: date-time
        in: query
        name: time
        type: string
      - description: 需要搜索的值，多个值英文逗号,分隔
        in: query
        name: search
        type: string
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.UserOperationLog'
                  type: array
              type: object
      summary: Query 根据指定字段、时间范围查询或搜索 UserOperationLog
      tags:
      - UserOperationLog
    post:
      consumes:
      - application/json
      description: Create 创建 UserOperationLog
      parameters:
      - description: UserOperationLog
        in: body
        name: useroperationlog
        required: true
        schema:
          $ref: '#/definitions/ent.UserOperationLog'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.UserOperationLog'
              type: object
      summary: Create 创建 UserOperationLog
      tags:
      - UserOperationLog
  /api/v1/useroperationlog/{id}:
    delete:
      consumes:
      - application/json
      description: DeleteByID 根据 ID 删除 UserOperationLog
      parameters:
      - description: UserOperationLog ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteByID 根据 ID 删除 UserOperationLog
      tags:
      - UserOperationLog
    get:
      consumes:
      - application/json
      description: QueryByID 根据 ID 查询 UserOperationLog
      parameters:
      - description: UserOperationLog ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.UserOperationLog'
              type: object
      summary: QueryByID 根据 ID 查询 UserOperationLog
      tags:
      - UserOperationLog
    put:
      consumes:
      - application/json
      description: UpdateByID 根据 ID 修改 UserOperationLog
      parameters:
      - description: UserOperationLog ID
        in: path
        name: id
        required: true
        type: integer
      - description: UserOperationLog
        in: body
        name: useroperationlog
        required: true
        schema:
          $ref: '#/definitions/ent.UserOperationLog'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.UserOperationLog'
              type: object
      summary: UpdateByID 根据 ID 修改 UserOperationLog
      tags:
      - UserOperationLog
  /api/v1/useroperationlog/bulk:
    post:
      consumes:
      - application/json
      description: CreateBulk 批量创建 UserOperationLog
      parameters:
      - description: UserOperationLog
        in: body
        name: useroperationlog
        required: true
        schema:
          items:
            $ref: '#/definitions/ent.UserOperationLog'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.UserOperationLog'
                  type: array
              type: object
      summary: CreateBulk 批量创建 UserOperationLog
      tags:
      - UserOperationLog
  /api/v1/useroperationlog/bulk/delete:
    post:
      consumes:
      - application/json
      description: DeleteBulk 根据 IDs 批量删除 UserOperationLog
      parameters:
      - description: 需要删除的id列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/common.DeleteItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteBulk 根据 IDs 批量删除 UserOperationLog
      tags:
      - UserOperationLog
  /api/v1/wofang:
    get:
      consumes:
      - application/json
      description: Query 根据指定字段、时间范围查询或搜索 Wofang
      parameters:
      - description: created_at
        format: date-time
        in: query
        name: created_at
        type: string
      - description: updated_at
        format: date-time
        in: query
        name: updated_at
        type: string
      - description: remark
        in: query
        name: remark
        type: string
      - description: name
        in: query
        name: name
        type: string
      - description: ip
        format: ipv4
        in: query
        name: ip
        type: string
      - description: type
        in: query
        name: type
        type: string
      - description: un_drag_second
        in: query
        name: un_drag_second
        type: integer
      - description: api_response
        in: query
        name: api_response
        type: string
      - description: status
        in: query
        name: status
        type: string
      - description: 需要搜索的值，多个值英文逗号,分隔
        in: query
        name: search
        type: string
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.Wofang'
                  type: array
              type: object
      summary: Query 根据指定字段、时间范围查询或搜索 Wofang
      tags:
      - Wofang
    post:
      consumes:
      - application/json
      description: Create 创建 Wofang
      parameters:
      - description: Wofang
        in: body
        name: wofang
        required: true
        schema:
          $ref: '#/definitions/ent.Wofang'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.Wofang'
              type: object
      summary: Create 创建 Wofang
      tags:
      - Wofang
  /api/v1/wofang/{id}:
    delete:
      consumes:
      - application/json
      description: DeleteByID 根据 ID 删除 Wofang
      parameters:
      - description: Wofang ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteByID 根据 ID 删除 Wofang
      tags:
      - Wofang
    get:
      consumes:
      - application/json
      description: QueryByID 根据 ID 查询 Wofang
      parameters:
      - description: Wofang ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.Wofang'
              type: object
      summary: QueryByID 根据 ID 查询 Wofang
      tags:
      - Wofang
    put:
      consumes:
      - application/json
      description: UpdateByID 根据 ID 修改 Wofang
      parameters:
      - description: Wofang ID
        in: path
        name: id
        required: true
        type: integer
      - description: Wofang
        in: body
        name: wofang
        required: true
        schema:
          $ref: '#/definitions/ent.Wofang'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.Wofang'
              type: object
      summary: UpdateByID 根据 ID 修改 Wofang
      tags:
      - Wofang
  /api/v1/wofang/api:
    get:
      consumes:
      - application/json
      description: Query 根据 牵引类型和ip 查询 沃防ip牵引状态
      parameters:
      - description: 牵引类型：hd | qy
        in: path
        name: dragType
        required: true
        type: string
      - description: 查询ip
        in: path
        name: ips
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/wofang.Response'
              type: object
      summary: Query 根据 牵引类型和ip 查询 沃防ip牵引状态
      tags:
      - Wofang
    post:
      consumes:
      - application/json
      description: Add 将ip添加到沃防牵引或黑洞清洗
      parameters:
      - description: 需要操作的数据
        in: body
        name: add
        required: true
        schema:
          $ref: '#/definitions/wofang.Add'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/wofang.Response'
              type: object
      summary: Add 将ip添加到沃防牵引或黑洞清洗
      tags:
      - Wofang
  /api/v1/wofang/api/{dragType}/{ip}:
    delete:
      consumes:
      - application/json
      description: Delete 将ip从沃防牵引或黑洞清洗 删除
      parameters:
      - description: 牵引类型
        in: path
        name: dragType
        required: true
        type: string
      - description: 需要删除的ip
        in: path
        name: ip
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/wofang.Response'
              type: object
      summary: Delete 将ip从沃防牵引或黑洞清洗 删除
      tags:
      - Wofang
  /api/v1/wofang/api2:
    get:
      consumes:
      - application/json
      description: Query 根据 ip 查询 沃防ip牵引状态
      parameters:
      - description: 查询ip
        in: path
        name: ips
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/wofang.Response'
              type: object
      summary: Query 根据 ip 查询 沃防ip牵引状态
      tags:
      - Wofang
    post:
      consumes:
      - application/json
      description: Add 将ip添加到沃防牵引
      parameters:
      - description: 需要操作的数据
        in: body
        name: add
        required: true
        schema:
          $ref: '#/definitions/wofang.Add'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/wofang.Response'
              type: object
      summary: Add 将ip添加到沃防牵引
      tags:
      - Wofang
  /api/v1/wofang/api2/{ip}:
    delete:
      consumes:
      - application/json
      description: Delete 将ip从沃防牵引 删除
      parameters:
      - description: 需要删除的ip
        in: path
        name: ip
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/wofang.Response'
              type: object
      summary: Delete 将ip从沃防牵引 删除
      tags:
      - Wofang
  /api/v1/wofang/bulk:
    post:
      consumes:
      - application/json
      description: CreateBulk 批量创建 Wofang
      parameters:
      - description: Wofang
        in: body
        name: wofang
        required: true
        schema:
          items:
            $ref: '#/definitions/ent.Wofang'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.Wofang'
                  type: array
              type: object
      summary: CreateBulk 批量创建 Wofang
      tags:
      - Wofang
  /api/v1/wofang/bulk/delete:
    post:
      consumes:
      - application/json
      description: DeleteBulk 根据 IDs 批量删除 Wofang
      parameters:
      - description: 需要删除的id列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/common.DeleteItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteBulk 根据 IDs 批量删除 Wofang
      tags:
      - Wofang
  /api/v1/wofangalert:
    get:
      consumes:
      - application/json
      description: Query 根据指定字段、时间范围查询或搜索 WofangAlert
      parameters:
      - description: created_at
        format: date-time
        in: query
        name: created_at
        type: string
      - description: updated_at
        format: date-time
        in: query
        name: updated_at
        type: string
      - description: remark
        in: query
        name: remark
        type: string
      - description: attack_status
        in: query
        name: attack_status
        type: string
      - description: attack_type
        in: query
        name: attack_type
        type: string
      - description: device_ip
        in: query
        name: device_ip
        type: string
      - description: zone_ip
        in: query
        name: zone_ip
        type: string
      - description: start_time
        format: date-time
        in: query
        name: start_time
        type: string
      - description: end_time
        format: date-time
        in: query
        name: end_time
        type: string
      - description: max_drop_kbps
        in: query
        name: max_drop_kbps
        type: integer
      - description: max_in_kbps
        in: query
        name: max_in_kbps
        type: integer
      - description: 需要搜索的值，多个值英文逗号,分隔
        in: query
        name: search
        type: string
      - description: 当前页
        in: query
        name: current
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        type: integer
      - description: 排序，默认id逆序(-id)
        in: query
        name: order
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.WofangAlert'
                  type: array
              type: object
      summary: Query 根据指定字段、时间范围查询或搜索 WofangAlert
      tags:
      - WofangAlert
    post:
      consumes:
      - application/json
      description: Create 创建 WofangAlert
      parameters:
      - description: WofangAlert
        in: body
        name: wofangalert
        required: true
        schema:
          $ref: '#/definitions/ent.WofangAlert'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.WofangAlert'
              type: object
      summary: Create 创建 WofangAlert
      tags:
      - WofangAlert
  /api/v1/wofangalert/{id}:
    delete:
      consumes:
      - application/json
      description: DeleteByID 根据 ID 删除 WofangAlert
      parameters:
      - description: WofangAlert ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteByID 根据 ID 删除 WofangAlert
      tags:
      - WofangAlert
    get:
      consumes:
      - application/json
      description: QueryByID 根据 ID 查询 WofangAlert
      parameters:
      - description: WofangAlert ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.WofangAlert'
              type: object
      summary: QueryByID 根据 ID 查询 WofangAlert
      tags:
      - WofangAlert
    put:
      consumes:
      - application/json
      description: UpdateByID 根据 ID 修改 WofangAlert
      parameters:
      - description: WofangAlert ID
        in: path
        name: id
        required: true
        type: integer
      - description: WofangAlert
        in: body
        name: wofangalert
        required: true
        schema:
          $ref: '#/definitions/ent.WofangAlert'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  $ref: '#/definitions/ent.WofangAlert'
              type: object
      summary: UpdateByID 根据 ID 修改 WofangAlert
      tags:
      - WofangAlert
  /api/v1/wofangalert/bulk:
    post:
      consumes:
      - application/json
      description: CreateBulk 批量创建 WofangAlert
      parameters:
      - description: WofangAlert
        in: body
        name: wofangalert
        required: true
        schema:
          items:
            $ref: '#/definitions/ent.WofangAlert'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/common.Result'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/ent.WofangAlert'
                  type: array
              type: object
      summary: CreateBulk 批量创建 WofangAlert
      tags:
      - WofangAlert
  /api/v1/wofangalert/bulk/delete:
    post:
      consumes:
      - application/json
      description: DeleteBulk 根据 IDs 批量删除 WofangAlert
      parameters:
      - description: 需要删除的id列表
        in: body
        name: ids
        required: true
        schema:
          $ref: '#/definitions/common.DeleteItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/common.Message'
      summary: DeleteBulk 根据 IDs 批量删除 WofangAlert
      tags:
      - WofangAlert
securityDefinitions:
  ApiKeyAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
