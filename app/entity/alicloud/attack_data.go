/**
* <AUTHOR>
* @date 2023-12-14 14:58
* @description AliCloud Origin Attack Data structures
 */

package alicloud

import "time"

// AliCloudOriginAttackDataMessage 阿里云原生DDoS攻击数据消息结构
type AliCloudOriginAttackDataMessage struct {
	EventType string    `json:"event_type"` // 事件类型
	Project   string    `json:"project"`    // IP 所属项目
	IP        string    `json:"ip"`         // 被攻击的 IP
	Region    string    `json:"region"`     // 区域
	Action    string    `json:"action"`     // 动作：add 事件开始; del 事件结束
	Time      time.Time `json:"time"`       // 事件时间
	Pps       int64     `json:"pps"`        // 报文数量大小
	Bps       int64     `json:"bps"`        // 流量大小
	DdosType  string    `json:"ddos_type"`  // 攻击事件类型：defense 清洗; blackhole 黑洞
}

// KafkaMessage Kafka消息结构
type KafkaMessage struct {
	Data []AliCloudOriginAttackDataMessage `json:"data"`
	Type string                            `json:"type"`
}
