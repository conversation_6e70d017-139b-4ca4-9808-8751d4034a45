// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/alicloudoriginattackdata"
	"meta/app/ent/predicate"
	"meta/app/ent/tenant"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// AliCloudOriginAttackDataUpdate is the builder for updating AliCloudOriginAttackData entities.
type AliCloudOriginAttackDataUpdate struct {
	config
	hooks    []Hook
	mutation *AliCloudOriginAttackDataMutation
}

// Where appends a list predicates to the AliCloudOriginAttackDataUpdate builder.
func (acoadu *AliCloudOriginAttackDataUpdate) Where(ps ...predicate.AliCloudOriginAttackData) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.Where(ps...)
	return acoadu
}

// SetTenantID sets the "tenant_id" field.
func (acoadu *AliCloudOriginAttackDataUpdate) SetTenantID(i int) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.SetTenantID(i)
	return acoadu
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (acoadu *AliCloudOriginAttackDataUpdate) SetNillableTenantID(i *int) *AliCloudOriginAttackDataUpdate {
	if i != nil {
		acoadu.SetTenantID(*i)
	}
	return acoadu
}

// ClearTenantID clears the value of the "tenant_id" field.
func (acoadu *AliCloudOriginAttackDataUpdate) ClearTenantID() *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.ClearTenantID()
	return acoadu
}

// SetUpdatedAt sets the "updated_at" field.
func (acoadu *AliCloudOriginAttackDataUpdate) SetUpdatedAt(t time.Time) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.SetUpdatedAt(t)
	return acoadu
}

// SetRemark sets the "remark" field.
func (acoadu *AliCloudOriginAttackDataUpdate) SetRemark(s string) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.SetRemark(s)
	return acoadu
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (acoadu *AliCloudOriginAttackDataUpdate) SetNillableRemark(s *string) *AliCloudOriginAttackDataUpdate {
	if s != nil {
		acoadu.SetRemark(*s)
	}
	return acoadu
}

// ClearRemark clears the value of the "remark" field.
func (acoadu *AliCloudOriginAttackDataUpdate) ClearRemark() *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.ClearRemark()
	return acoadu
}

// SetEventType sets the "event_type" field.
func (acoadu *AliCloudOriginAttackDataUpdate) SetEventType(s string) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.SetEventType(s)
	return acoadu
}

// SetNillableEventType sets the "event_type" field if the given value is not nil.
func (acoadu *AliCloudOriginAttackDataUpdate) SetNillableEventType(s *string) *AliCloudOriginAttackDataUpdate {
	if s != nil {
		acoadu.SetEventType(*s)
	}
	return acoadu
}

// SetProject sets the "project" field.
func (acoadu *AliCloudOriginAttackDataUpdate) SetProject(s string) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.SetProject(s)
	return acoadu
}

// SetNillableProject sets the "project" field if the given value is not nil.
func (acoadu *AliCloudOriginAttackDataUpdate) SetNillableProject(s *string) *AliCloudOriginAttackDataUpdate {
	if s != nil {
		acoadu.SetProject(*s)
	}
	return acoadu
}

// SetIP sets the "ip" field.
func (acoadu *AliCloudOriginAttackDataUpdate) SetIP(s string) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.SetIP(s)
	return acoadu
}

// SetNillableIP sets the "ip" field if the given value is not nil.
func (acoadu *AliCloudOriginAttackDataUpdate) SetNillableIP(s *string) *AliCloudOriginAttackDataUpdate {
	if s != nil {
		acoadu.SetIP(*s)
	}
	return acoadu
}

// SetRegion sets the "region" field.
func (acoadu *AliCloudOriginAttackDataUpdate) SetRegion(s string) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.SetRegion(s)
	return acoadu
}

// SetNillableRegion sets the "region" field if the given value is not nil.
func (acoadu *AliCloudOriginAttackDataUpdate) SetNillableRegion(s *string) *AliCloudOriginAttackDataUpdate {
	if s != nil {
		acoadu.SetRegion(*s)
	}
	return acoadu
}

// SetAction sets the "action" field.
func (acoadu *AliCloudOriginAttackDataUpdate) SetAction(s string) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.SetAction(s)
	return acoadu
}

// SetNillableAction sets the "action" field if the given value is not nil.
func (acoadu *AliCloudOriginAttackDataUpdate) SetNillableAction(s *string) *AliCloudOriginAttackDataUpdate {
	if s != nil {
		acoadu.SetAction(*s)
	}
	return acoadu
}

// SetTime sets the "time" field.
func (acoadu *AliCloudOriginAttackDataUpdate) SetTime(t time.Time) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.SetTime(t)
	return acoadu
}

// SetNillableTime sets the "time" field if the given value is not nil.
func (acoadu *AliCloudOriginAttackDataUpdate) SetNillableTime(t *time.Time) *AliCloudOriginAttackDataUpdate {
	if t != nil {
		acoadu.SetTime(*t)
	}
	return acoadu
}

// SetPps sets the "pps" field.
func (acoadu *AliCloudOriginAttackDataUpdate) SetPps(i int64) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.ResetPps()
	acoadu.mutation.SetPps(i)
	return acoadu
}

// SetNillablePps sets the "pps" field if the given value is not nil.
func (acoadu *AliCloudOriginAttackDataUpdate) SetNillablePps(i *int64) *AliCloudOriginAttackDataUpdate {
	if i != nil {
		acoadu.SetPps(*i)
	}
	return acoadu
}

// AddPps adds i to the "pps" field.
func (acoadu *AliCloudOriginAttackDataUpdate) AddPps(i int64) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.AddPps(i)
	return acoadu
}

// SetBps sets the "bps" field.
func (acoadu *AliCloudOriginAttackDataUpdate) SetBps(i int64) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.ResetBps()
	acoadu.mutation.SetBps(i)
	return acoadu
}

// SetNillableBps sets the "bps" field if the given value is not nil.
func (acoadu *AliCloudOriginAttackDataUpdate) SetNillableBps(i *int64) *AliCloudOriginAttackDataUpdate {
	if i != nil {
		acoadu.SetBps(*i)
	}
	return acoadu
}

// AddBps adds i to the "bps" field.
func (acoadu *AliCloudOriginAttackDataUpdate) AddBps(i int64) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.AddBps(i)
	return acoadu
}

// SetDdosType sets the "ddos_type" field.
func (acoadu *AliCloudOriginAttackDataUpdate) SetDdosType(s string) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.SetDdosType(s)
	return acoadu
}

// SetNillableDdosType sets the "ddos_type" field if the given value is not nil.
func (acoadu *AliCloudOriginAttackDataUpdate) SetNillableDdosType(s *string) *AliCloudOriginAttackDataUpdate {
	if s != nil {
		acoadu.SetDdosType(*s)
	}
	return acoadu
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (acoadu *AliCloudOriginAttackDataUpdate) SetTenant(t *Tenant) *AliCloudOriginAttackDataUpdate {
	return acoadu.SetTenantID(t.ID)
}

// Mutation returns the AliCloudOriginAttackDataMutation object of the builder.
func (acoadu *AliCloudOriginAttackDataUpdate) Mutation() *AliCloudOriginAttackDataMutation {
	return acoadu.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (acoadu *AliCloudOriginAttackDataUpdate) ClearTenant() *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.ClearTenant()
	return acoadu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (acoadu *AliCloudOriginAttackDataUpdate) Save(ctx context.Context) (int, error) {
	if err := acoadu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, acoadu.sqlSave, acoadu.mutation, acoadu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (acoadu *AliCloudOriginAttackDataUpdate) SaveX(ctx context.Context) int {
	affected, err := acoadu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (acoadu *AliCloudOriginAttackDataUpdate) Exec(ctx context.Context) error {
	_, err := acoadu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (acoadu *AliCloudOriginAttackDataUpdate) ExecX(ctx context.Context) {
	if err := acoadu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (acoadu *AliCloudOriginAttackDataUpdate) defaults() error {
	if _, ok := acoadu.mutation.UpdatedAt(); !ok {
		if alicloudoriginattackdata.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized alicloudoriginattackdata.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := alicloudoriginattackdata.UpdateDefaultUpdatedAt()
		acoadu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (acoadu *AliCloudOriginAttackDataUpdate) check() error {
	if v, ok := acoadu.mutation.Remark(); ok {
		if err := alicloudoriginattackdata.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "AliCloudOriginAttackData.remark": %w`, err)}
		}
	}
	return nil
}

func (acoadu *AliCloudOriginAttackDataUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := acoadu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(alicloudoriginattackdata.Table, alicloudoriginattackdata.Columns, sqlgraph.NewFieldSpec(alicloudoriginattackdata.FieldID, field.TypeInt))
	if ps := acoadu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := acoadu.mutation.UpdatedAt(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := acoadu.mutation.Remark(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldRemark, field.TypeString, value)
	}
	if acoadu.mutation.RemarkCleared() {
		_spec.ClearField(alicloudoriginattackdata.FieldRemark, field.TypeString)
	}
	if value, ok := acoadu.mutation.EventType(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldEventType, field.TypeString, value)
	}
	if value, ok := acoadu.mutation.Project(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldProject, field.TypeString, value)
	}
	if value, ok := acoadu.mutation.IP(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldIP, field.TypeString, value)
	}
	if value, ok := acoadu.mutation.Region(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldRegion, field.TypeString, value)
	}
	if value, ok := acoadu.mutation.Action(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldAction, field.TypeString, value)
	}
	if value, ok := acoadu.mutation.Time(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldTime, field.TypeTime, value)
	}
	if value, ok := acoadu.mutation.Pps(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldPps, field.TypeInt64, value)
	}
	if value, ok := acoadu.mutation.AddedPps(); ok {
		_spec.AddField(alicloudoriginattackdata.FieldPps, field.TypeInt64, value)
	}
	if value, ok := acoadu.mutation.Bps(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldBps, field.TypeInt64, value)
	}
	if value, ok := acoadu.mutation.AddedBps(); ok {
		_spec.AddField(alicloudoriginattackdata.FieldBps, field.TypeInt64, value)
	}
	if value, ok := acoadu.mutation.DdosType(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldDdosType, field.TypeString, value)
	}
	if acoadu.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   alicloudoriginattackdata.TenantTable,
			Columns: []string{alicloudoriginattackdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := acoadu.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   alicloudoriginattackdata.TenantTable,
			Columns: []string{alicloudoriginattackdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, acoadu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{alicloudoriginattackdata.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	acoadu.mutation.done = true
	return n, nil
}

// AliCloudOriginAttackDataUpdateOne is the builder for updating a single AliCloudOriginAttackData entity.
type AliCloudOriginAttackDataUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *AliCloudOriginAttackDataMutation
}

// SetTenantID sets the "tenant_id" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetTenantID(i int) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.SetTenantID(i)
	return acoaduo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetNillableTenantID(i *int) *AliCloudOriginAttackDataUpdateOne {
	if i != nil {
		acoaduo.SetTenantID(*i)
	}
	return acoaduo
}

// ClearTenantID clears the value of the "tenant_id" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) ClearTenantID() *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.ClearTenantID()
	return acoaduo
}

// SetUpdatedAt sets the "updated_at" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetUpdatedAt(t time.Time) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.SetUpdatedAt(t)
	return acoaduo
}

// SetRemark sets the "remark" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetRemark(s string) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.SetRemark(s)
	return acoaduo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetNillableRemark(s *string) *AliCloudOriginAttackDataUpdateOne {
	if s != nil {
		acoaduo.SetRemark(*s)
	}
	return acoaduo
}

// ClearRemark clears the value of the "remark" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) ClearRemark() *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.ClearRemark()
	return acoaduo
}

// SetEventType sets the "event_type" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetEventType(s string) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.SetEventType(s)
	return acoaduo
}

// SetNillableEventType sets the "event_type" field if the given value is not nil.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetNillableEventType(s *string) *AliCloudOriginAttackDataUpdateOne {
	if s != nil {
		acoaduo.SetEventType(*s)
	}
	return acoaduo
}

// SetProject sets the "project" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetProject(s string) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.SetProject(s)
	return acoaduo
}

// SetNillableProject sets the "project" field if the given value is not nil.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetNillableProject(s *string) *AliCloudOriginAttackDataUpdateOne {
	if s != nil {
		acoaduo.SetProject(*s)
	}
	return acoaduo
}

// SetIP sets the "ip" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetIP(s string) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.SetIP(s)
	return acoaduo
}

// SetNillableIP sets the "ip" field if the given value is not nil.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetNillableIP(s *string) *AliCloudOriginAttackDataUpdateOne {
	if s != nil {
		acoaduo.SetIP(*s)
	}
	return acoaduo
}

// SetRegion sets the "region" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetRegion(s string) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.SetRegion(s)
	return acoaduo
}

// SetNillableRegion sets the "region" field if the given value is not nil.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetNillableRegion(s *string) *AliCloudOriginAttackDataUpdateOne {
	if s != nil {
		acoaduo.SetRegion(*s)
	}
	return acoaduo
}

// SetAction sets the "action" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetAction(s string) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.SetAction(s)
	return acoaduo
}

// SetNillableAction sets the "action" field if the given value is not nil.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetNillableAction(s *string) *AliCloudOriginAttackDataUpdateOne {
	if s != nil {
		acoaduo.SetAction(*s)
	}
	return acoaduo
}

// SetTime sets the "time" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetTime(t time.Time) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.SetTime(t)
	return acoaduo
}

// SetNillableTime sets the "time" field if the given value is not nil.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetNillableTime(t *time.Time) *AliCloudOriginAttackDataUpdateOne {
	if t != nil {
		acoaduo.SetTime(*t)
	}
	return acoaduo
}

// SetPps sets the "pps" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetPps(i int64) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.ResetPps()
	acoaduo.mutation.SetPps(i)
	return acoaduo
}

// SetNillablePps sets the "pps" field if the given value is not nil.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetNillablePps(i *int64) *AliCloudOriginAttackDataUpdateOne {
	if i != nil {
		acoaduo.SetPps(*i)
	}
	return acoaduo
}

// AddPps adds i to the "pps" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) AddPps(i int64) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.AddPps(i)
	return acoaduo
}

// SetBps sets the "bps" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetBps(i int64) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.ResetBps()
	acoaduo.mutation.SetBps(i)
	return acoaduo
}

// SetNillableBps sets the "bps" field if the given value is not nil.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetNillableBps(i *int64) *AliCloudOriginAttackDataUpdateOne {
	if i != nil {
		acoaduo.SetBps(*i)
	}
	return acoaduo
}

// AddBps adds i to the "bps" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) AddBps(i int64) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.AddBps(i)
	return acoaduo
}

// SetDdosType sets the "ddos_type" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetDdosType(s string) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.SetDdosType(s)
	return acoaduo
}

// SetNillableDdosType sets the "ddos_type" field if the given value is not nil.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetNillableDdosType(s *string) *AliCloudOriginAttackDataUpdateOne {
	if s != nil {
		acoaduo.SetDdosType(*s)
	}
	return acoaduo
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetTenant(t *Tenant) *AliCloudOriginAttackDataUpdateOne {
	return acoaduo.SetTenantID(t.ID)
}

// Mutation returns the AliCloudOriginAttackDataMutation object of the builder.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) Mutation() *AliCloudOriginAttackDataMutation {
	return acoaduo.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) ClearTenant() *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.ClearTenant()
	return acoaduo
}

// Where appends a list predicates to the AliCloudOriginAttackDataUpdate builder.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) Where(ps ...predicate.AliCloudOriginAttackData) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.Where(ps...)
	return acoaduo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) Select(field string, fields ...string) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.fields = append([]string{field}, fields...)
	return acoaduo
}

// Save executes the query and returns the updated AliCloudOriginAttackData entity.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) Save(ctx context.Context) (*AliCloudOriginAttackData, error) {
	if err := acoaduo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, acoaduo.sqlSave, acoaduo.mutation, acoaduo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SaveX(ctx context.Context) *AliCloudOriginAttackData {
	node, err := acoaduo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) Exec(ctx context.Context) error {
	_, err := acoaduo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) ExecX(ctx context.Context) {
	if err := acoaduo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) defaults() error {
	if _, ok := acoaduo.mutation.UpdatedAt(); !ok {
		if alicloudoriginattackdata.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized alicloudoriginattackdata.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := alicloudoriginattackdata.UpdateDefaultUpdatedAt()
		acoaduo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) check() error {
	if v, ok := acoaduo.mutation.Remark(); ok {
		if err := alicloudoriginattackdata.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "AliCloudOriginAttackData.remark": %w`, err)}
		}
	}
	return nil
}

func (acoaduo *AliCloudOriginAttackDataUpdateOne) sqlSave(ctx context.Context) (_node *AliCloudOriginAttackData, err error) {
	if err := acoaduo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(alicloudoriginattackdata.Table, alicloudoriginattackdata.Columns, sqlgraph.NewFieldSpec(alicloudoriginattackdata.FieldID, field.TypeInt))
	id, ok := acoaduo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "AliCloudOriginAttackData.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := acoaduo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, alicloudoriginattackdata.FieldID)
		for _, f := range fields {
			if !alicloudoriginattackdata.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != alicloudoriginattackdata.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := acoaduo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := acoaduo.mutation.UpdatedAt(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := acoaduo.mutation.Remark(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldRemark, field.TypeString, value)
	}
	if acoaduo.mutation.RemarkCleared() {
		_spec.ClearField(alicloudoriginattackdata.FieldRemark, field.TypeString)
	}
	if value, ok := acoaduo.mutation.EventType(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldEventType, field.TypeString, value)
	}
	if value, ok := acoaduo.mutation.Project(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldProject, field.TypeString, value)
	}
	if value, ok := acoaduo.mutation.IP(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldIP, field.TypeString, value)
	}
	if value, ok := acoaduo.mutation.Region(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldRegion, field.TypeString, value)
	}
	if value, ok := acoaduo.mutation.Action(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldAction, field.TypeString, value)
	}
	if value, ok := acoaduo.mutation.Time(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldTime, field.TypeTime, value)
	}
	if value, ok := acoaduo.mutation.Pps(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldPps, field.TypeInt64, value)
	}
	if value, ok := acoaduo.mutation.AddedPps(); ok {
		_spec.AddField(alicloudoriginattackdata.FieldPps, field.TypeInt64, value)
	}
	if value, ok := acoaduo.mutation.Bps(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldBps, field.TypeInt64, value)
	}
	if value, ok := acoaduo.mutation.AddedBps(); ok {
		_spec.AddField(alicloudoriginattackdata.FieldBps, field.TypeInt64, value)
	}
	if value, ok := acoaduo.mutation.DdosType(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldDdosType, field.TypeString, value)
	}
	if acoaduo.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   alicloudoriginattackdata.TenantTable,
			Columns: []string{alicloudoriginattackdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := acoaduo.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   alicloudoriginattackdata.TenantTable,
			Columns: []string{alicloudoriginattackdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &AliCloudOriginAttackData{config: acoaduo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, acoaduo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{alicloudoriginattackdata.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	acoaduo.mutation.done = true
	return _node, nil
}
