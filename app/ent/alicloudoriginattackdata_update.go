// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/alicloudoriginattackdata"
	"meta/app/ent/predicate"
	"meta/app/ent/tenant"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// AliCloudOriginAttackDataUpdate is the builder for updating AliCloudOriginAttackData entities.
type AliCloudOriginAttackDataUpdate struct {
	config
	hooks    []Hook
	mutation *AliCloudOriginAttackDataMutation
}

// Where appends a list predicates to the AliCloudOriginAttackDataUpdate builder.
func (acoadu *AliCloudOriginAttackDataUpdate) Where(ps ...predicate.AliCloudOriginAttackData) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.Where(ps...)
	return acoadu
}

// SetTenantID sets the "tenant_id" field.
func (acoadu *AliCloudOriginAttackDataUpdate) SetTenantID(i int) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.SetTenantID(i)
	return acoadu
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (acoadu *AliCloudOriginAttackDataUpdate) SetNillableTenantID(i *int) *AliCloudOriginAttackDataUpdate {
	if i != nil {
		acoadu.SetTenantID(*i)
	}
	return acoadu
}

// ClearTenantID clears the value of the "tenant_id" field.
func (acoadu *AliCloudOriginAttackDataUpdate) ClearTenantID() *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.ClearTenantID()
	return acoadu
}

// SetUpdatedAt sets the "updated_at" field.
func (acoadu *AliCloudOriginAttackDataUpdate) SetUpdatedAt(t time.Time) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.SetUpdatedAt(t)
	return acoadu
}

// SetRemark sets the "remark" field.
func (acoadu *AliCloudOriginAttackDataUpdate) SetRemark(s string) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.SetRemark(s)
	return acoadu
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (acoadu *AliCloudOriginAttackDataUpdate) SetNillableRemark(s *string) *AliCloudOriginAttackDataUpdate {
	if s != nil {
		acoadu.SetRemark(*s)
	}
	return acoadu
}

// ClearRemark clears the value of the "remark" field.
func (acoadu *AliCloudOriginAttackDataUpdate) ClearRemark() *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.ClearRemark()
	return acoadu
}

// SetProject sets the "project" field.
func (acoadu *AliCloudOriginAttackDataUpdate) SetProject(s string) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.SetProject(s)
	return acoadu
}

// SetNillableProject sets the "project" field if the given value is not nil.
func (acoadu *AliCloudOriginAttackDataUpdate) SetNillableProject(s *string) *AliCloudOriginAttackDataUpdate {
	if s != nil {
		acoadu.SetProject(*s)
	}
	return acoadu
}

// SetCloudType sets the "cloud_type" field.
func (acoadu *AliCloudOriginAttackDataUpdate) SetCloudType(s string) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.SetCloudType(s)
	return acoadu
}

// SetNillableCloudType sets the "cloud_type" field if the given value is not nil.
func (acoadu *AliCloudOriginAttackDataUpdate) SetNillableCloudType(s *string) *AliCloudOriginAttackDataUpdate {
	if s != nil {
		acoadu.SetCloudType(*s)
	}
	return acoadu
}

// SetIP sets the "ip" field.
func (acoadu *AliCloudOriginAttackDataUpdate) SetIP(s string) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.SetIP(s)
	return acoadu
}

// SetNillableIP sets the "ip" field if the given value is not nil.
func (acoadu *AliCloudOriginAttackDataUpdate) SetNillableIP(s *string) *AliCloudOriginAttackDataUpdate {
	if s != nil {
		acoadu.SetIP(*s)
	}
	return acoadu
}

// SetStartTime sets the "start_time" field.
func (acoadu *AliCloudOriginAttackDataUpdate) SetStartTime(t time.Time) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.SetStartTime(t)
	return acoadu
}

// SetNillableStartTime sets the "start_time" field if the given value is not nil.
func (acoadu *AliCloudOriginAttackDataUpdate) SetNillableStartTime(t *time.Time) *AliCloudOriginAttackDataUpdate {
	if t != nil {
		acoadu.SetStartTime(*t)
	}
	return acoadu
}

// SetEndTime sets the "end_time" field.
func (acoadu *AliCloudOriginAttackDataUpdate) SetEndTime(t time.Time) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.SetEndTime(t)
	return acoadu
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (acoadu *AliCloudOriginAttackDataUpdate) SetNillableEndTime(t *time.Time) *AliCloudOriginAttackDataUpdate {
	if t != nil {
		acoadu.SetEndTime(*t)
	}
	return acoadu
}

// ClearEndTime clears the value of the "end_time" field.
func (acoadu *AliCloudOriginAttackDataUpdate) ClearEndTime() *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.ClearEndTime()
	return acoadu
}

// SetDuration sets the "duration" field.
func (acoadu *AliCloudOriginAttackDataUpdate) SetDuration(i int) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.ResetDuration()
	acoadu.mutation.SetDuration(i)
	return acoadu
}

// SetNillableDuration sets the "duration" field if the given value is not nil.
func (acoadu *AliCloudOriginAttackDataUpdate) SetNillableDuration(i *int) *AliCloudOriginAttackDataUpdate {
	if i != nil {
		acoadu.SetDuration(*i)
	}
	return acoadu
}

// AddDuration adds i to the "duration" field.
func (acoadu *AliCloudOriginAttackDataUpdate) AddDuration(i int) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.AddDuration(i)
	return acoadu
}

// ClearDuration clears the value of the "duration" field.
func (acoadu *AliCloudOriginAttackDataUpdate) ClearDuration() *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.ClearDuration()
	return acoadu
}

// SetPps sets the "pps" field.
func (acoadu *AliCloudOriginAttackDataUpdate) SetPps(i int64) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.ResetPps()
	acoadu.mutation.SetPps(i)
	return acoadu
}

// SetNillablePps sets the "pps" field if the given value is not nil.
func (acoadu *AliCloudOriginAttackDataUpdate) SetNillablePps(i *int64) *AliCloudOriginAttackDataUpdate {
	if i != nil {
		acoadu.SetPps(*i)
	}
	return acoadu
}

// AddPps adds i to the "pps" field.
func (acoadu *AliCloudOriginAttackDataUpdate) AddPps(i int64) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.AddPps(i)
	return acoadu
}

// SetMbps sets the "mbps" field.
func (acoadu *AliCloudOriginAttackDataUpdate) SetMbps(i int64) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.ResetMbps()
	acoadu.mutation.SetMbps(i)
	return acoadu
}

// SetNillableMbps sets the "mbps" field if the given value is not nil.
func (acoadu *AliCloudOriginAttackDataUpdate) SetNillableMbps(i *int64) *AliCloudOriginAttackDataUpdate {
	if i != nil {
		acoadu.SetMbps(*i)
	}
	return acoadu
}

// AddMbps adds i to the "mbps" field.
func (acoadu *AliCloudOriginAttackDataUpdate) AddMbps(i int64) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.AddMbps(i)
	return acoadu
}

// SetStatus sets the "status" field.
func (acoadu *AliCloudOriginAttackDataUpdate) SetStatus(s string) *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.SetStatus(s)
	return acoadu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (acoadu *AliCloudOriginAttackDataUpdate) SetNillableStatus(s *string) *AliCloudOriginAttackDataUpdate {
	if s != nil {
		acoadu.SetStatus(*s)
	}
	return acoadu
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (acoadu *AliCloudOriginAttackDataUpdate) SetTenant(t *Tenant) *AliCloudOriginAttackDataUpdate {
	return acoadu.SetTenantID(t.ID)
}

// Mutation returns the AliCloudOriginAttackDataMutation object of the builder.
func (acoadu *AliCloudOriginAttackDataUpdate) Mutation() *AliCloudOriginAttackDataMutation {
	return acoadu.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (acoadu *AliCloudOriginAttackDataUpdate) ClearTenant() *AliCloudOriginAttackDataUpdate {
	acoadu.mutation.ClearTenant()
	return acoadu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (acoadu *AliCloudOriginAttackDataUpdate) Save(ctx context.Context) (int, error) {
	if err := acoadu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, acoadu.sqlSave, acoadu.mutation, acoadu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (acoadu *AliCloudOriginAttackDataUpdate) SaveX(ctx context.Context) int {
	affected, err := acoadu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (acoadu *AliCloudOriginAttackDataUpdate) Exec(ctx context.Context) error {
	_, err := acoadu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (acoadu *AliCloudOriginAttackDataUpdate) ExecX(ctx context.Context) {
	if err := acoadu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (acoadu *AliCloudOriginAttackDataUpdate) defaults() error {
	if _, ok := acoadu.mutation.UpdatedAt(); !ok {
		if alicloudoriginattackdata.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized alicloudoriginattackdata.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := alicloudoriginattackdata.UpdateDefaultUpdatedAt()
		acoadu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (acoadu *AliCloudOriginAttackDataUpdate) check() error {
	if v, ok := acoadu.mutation.Remark(); ok {
		if err := alicloudoriginattackdata.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "AliCloudOriginAttackData.remark": %w`, err)}
		}
	}
	return nil
}

func (acoadu *AliCloudOriginAttackDataUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := acoadu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(alicloudoriginattackdata.Table, alicloudoriginattackdata.Columns, sqlgraph.NewFieldSpec(alicloudoriginattackdata.FieldID, field.TypeInt))
	if ps := acoadu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := acoadu.mutation.UpdatedAt(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := acoadu.mutation.Remark(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldRemark, field.TypeString, value)
	}
	if acoadu.mutation.RemarkCleared() {
		_spec.ClearField(alicloudoriginattackdata.FieldRemark, field.TypeString)
	}
	if value, ok := acoadu.mutation.Project(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldProject, field.TypeString, value)
	}
	if value, ok := acoadu.mutation.CloudType(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldCloudType, field.TypeString, value)
	}
	if value, ok := acoadu.mutation.IP(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldIP, field.TypeString, value)
	}
	if value, ok := acoadu.mutation.StartTime(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldStartTime, field.TypeTime, value)
	}
	if value, ok := acoadu.mutation.EndTime(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldEndTime, field.TypeTime, value)
	}
	if acoadu.mutation.EndTimeCleared() {
		_spec.ClearField(alicloudoriginattackdata.FieldEndTime, field.TypeTime)
	}
	if value, ok := acoadu.mutation.Duration(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldDuration, field.TypeInt, value)
	}
	if value, ok := acoadu.mutation.AddedDuration(); ok {
		_spec.AddField(alicloudoriginattackdata.FieldDuration, field.TypeInt, value)
	}
	if acoadu.mutation.DurationCleared() {
		_spec.ClearField(alicloudoriginattackdata.FieldDuration, field.TypeInt)
	}
	if value, ok := acoadu.mutation.Pps(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldPps, field.TypeInt64, value)
	}
	if value, ok := acoadu.mutation.AddedPps(); ok {
		_spec.AddField(alicloudoriginattackdata.FieldPps, field.TypeInt64, value)
	}
	if value, ok := acoadu.mutation.Mbps(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldMbps, field.TypeInt64, value)
	}
	if value, ok := acoadu.mutation.AddedMbps(); ok {
		_spec.AddField(alicloudoriginattackdata.FieldMbps, field.TypeInt64, value)
	}
	if value, ok := acoadu.mutation.Status(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldStatus, field.TypeString, value)
	}
	if acoadu.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   alicloudoriginattackdata.TenantTable,
			Columns: []string{alicloudoriginattackdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := acoadu.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   alicloudoriginattackdata.TenantTable,
			Columns: []string{alicloudoriginattackdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, acoadu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{alicloudoriginattackdata.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	acoadu.mutation.done = true
	return n, nil
}

// AliCloudOriginAttackDataUpdateOne is the builder for updating a single AliCloudOriginAttackData entity.
type AliCloudOriginAttackDataUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *AliCloudOriginAttackDataMutation
}

// SetTenantID sets the "tenant_id" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetTenantID(i int) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.SetTenantID(i)
	return acoaduo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetNillableTenantID(i *int) *AliCloudOriginAttackDataUpdateOne {
	if i != nil {
		acoaduo.SetTenantID(*i)
	}
	return acoaduo
}

// ClearTenantID clears the value of the "tenant_id" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) ClearTenantID() *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.ClearTenantID()
	return acoaduo
}

// SetUpdatedAt sets the "updated_at" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetUpdatedAt(t time.Time) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.SetUpdatedAt(t)
	return acoaduo
}

// SetRemark sets the "remark" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetRemark(s string) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.SetRemark(s)
	return acoaduo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetNillableRemark(s *string) *AliCloudOriginAttackDataUpdateOne {
	if s != nil {
		acoaduo.SetRemark(*s)
	}
	return acoaduo
}

// ClearRemark clears the value of the "remark" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) ClearRemark() *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.ClearRemark()
	return acoaduo
}

// SetProject sets the "project" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetProject(s string) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.SetProject(s)
	return acoaduo
}

// SetNillableProject sets the "project" field if the given value is not nil.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetNillableProject(s *string) *AliCloudOriginAttackDataUpdateOne {
	if s != nil {
		acoaduo.SetProject(*s)
	}
	return acoaduo
}

// SetCloudType sets the "cloud_type" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetCloudType(s string) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.SetCloudType(s)
	return acoaduo
}

// SetNillableCloudType sets the "cloud_type" field if the given value is not nil.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetNillableCloudType(s *string) *AliCloudOriginAttackDataUpdateOne {
	if s != nil {
		acoaduo.SetCloudType(*s)
	}
	return acoaduo
}

// SetIP sets the "ip" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetIP(s string) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.SetIP(s)
	return acoaduo
}

// SetNillableIP sets the "ip" field if the given value is not nil.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetNillableIP(s *string) *AliCloudOriginAttackDataUpdateOne {
	if s != nil {
		acoaduo.SetIP(*s)
	}
	return acoaduo
}

// SetStartTime sets the "start_time" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetStartTime(t time.Time) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.SetStartTime(t)
	return acoaduo
}

// SetNillableStartTime sets the "start_time" field if the given value is not nil.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetNillableStartTime(t *time.Time) *AliCloudOriginAttackDataUpdateOne {
	if t != nil {
		acoaduo.SetStartTime(*t)
	}
	return acoaduo
}

// SetEndTime sets the "end_time" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetEndTime(t time.Time) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.SetEndTime(t)
	return acoaduo
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetNillableEndTime(t *time.Time) *AliCloudOriginAttackDataUpdateOne {
	if t != nil {
		acoaduo.SetEndTime(*t)
	}
	return acoaduo
}

// ClearEndTime clears the value of the "end_time" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) ClearEndTime() *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.ClearEndTime()
	return acoaduo
}

// SetDuration sets the "duration" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetDuration(i int) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.ResetDuration()
	acoaduo.mutation.SetDuration(i)
	return acoaduo
}

// SetNillableDuration sets the "duration" field if the given value is not nil.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetNillableDuration(i *int) *AliCloudOriginAttackDataUpdateOne {
	if i != nil {
		acoaduo.SetDuration(*i)
	}
	return acoaduo
}

// AddDuration adds i to the "duration" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) AddDuration(i int) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.AddDuration(i)
	return acoaduo
}

// ClearDuration clears the value of the "duration" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) ClearDuration() *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.ClearDuration()
	return acoaduo
}

// SetPps sets the "pps" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetPps(i int64) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.ResetPps()
	acoaduo.mutation.SetPps(i)
	return acoaduo
}

// SetNillablePps sets the "pps" field if the given value is not nil.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetNillablePps(i *int64) *AliCloudOriginAttackDataUpdateOne {
	if i != nil {
		acoaduo.SetPps(*i)
	}
	return acoaduo
}

// AddPps adds i to the "pps" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) AddPps(i int64) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.AddPps(i)
	return acoaduo
}

// SetMbps sets the "mbps" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetMbps(i int64) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.ResetMbps()
	acoaduo.mutation.SetMbps(i)
	return acoaduo
}

// SetNillableMbps sets the "mbps" field if the given value is not nil.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetNillableMbps(i *int64) *AliCloudOriginAttackDataUpdateOne {
	if i != nil {
		acoaduo.SetMbps(*i)
	}
	return acoaduo
}

// AddMbps adds i to the "mbps" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) AddMbps(i int64) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.AddMbps(i)
	return acoaduo
}

// SetStatus sets the "status" field.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetStatus(s string) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.SetStatus(s)
	return acoaduo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetNillableStatus(s *string) *AliCloudOriginAttackDataUpdateOne {
	if s != nil {
		acoaduo.SetStatus(*s)
	}
	return acoaduo
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SetTenant(t *Tenant) *AliCloudOriginAttackDataUpdateOne {
	return acoaduo.SetTenantID(t.ID)
}

// Mutation returns the AliCloudOriginAttackDataMutation object of the builder.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) Mutation() *AliCloudOriginAttackDataMutation {
	return acoaduo.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) ClearTenant() *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.ClearTenant()
	return acoaduo
}

// Where appends a list predicates to the AliCloudOriginAttackDataUpdate builder.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) Where(ps ...predicate.AliCloudOriginAttackData) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.mutation.Where(ps...)
	return acoaduo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) Select(field string, fields ...string) *AliCloudOriginAttackDataUpdateOne {
	acoaduo.fields = append([]string{field}, fields...)
	return acoaduo
}

// Save executes the query and returns the updated AliCloudOriginAttackData entity.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) Save(ctx context.Context) (*AliCloudOriginAttackData, error) {
	if err := acoaduo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, acoaduo.sqlSave, acoaduo.mutation, acoaduo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) SaveX(ctx context.Context) *AliCloudOriginAttackData {
	node, err := acoaduo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) Exec(ctx context.Context) error {
	_, err := acoaduo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) ExecX(ctx context.Context) {
	if err := acoaduo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) defaults() error {
	if _, ok := acoaduo.mutation.UpdatedAt(); !ok {
		if alicloudoriginattackdata.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized alicloudoriginattackdata.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := alicloudoriginattackdata.UpdateDefaultUpdatedAt()
		acoaduo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (acoaduo *AliCloudOriginAttackDataUpdateOne) check() error {
	if v, ok := acoaduo.mutation.Remark(); ok {
		if err := alicloudoriginattackdata.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "AliCloudOriginAttackData.remark": %w`, err)}
		}
	}
	return nil
}

func (acoaduo *AliCloudOriginAttackDataUpdateOne) sqlSave(ctx context.Context) (_node *AliCloudOriginAttackData, err error) {
	if err := acoaduo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(alicloudoriginattackdata.Table, alicloudoriginattackdata.Columns, sqlgraph.NewFieldSpec(alicloudoriginattackdata.FieldID, field.TypeInt))
	id, ok := acoaduo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "AliCloudOriginAttackData.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := acoaduo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, alicloudoriginattackdata.FieldID)
		for _, f := range fields {
			if !alicloudoriginattackdata.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != alicloudoriginattackdata.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := acoaduo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := acoaduo.mutation.UpdatedAt(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := acoaduo.mutation.Remark(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldRemark, field.TypeString, value)
	}
	if acoaduo.mutation.RemarkCleared() {
		_spec.ClearField(alicloudoriginattackdata.FieldRemark, field.TypeString)
	}
	if value, ok := acoaduo.mutation.Project(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldProject, field.TypeString, value)
	}
	if value, ok := acoaduo.mutation.CloudType(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldCloudType, field.TypeString, value)
	}
	if value, ok := acoaduo.mutation.IP(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldIP, field.TypeString, value)
	}
	if value, ok := acoaduo.mutation.StartTime(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldStartTime, field.TypeTime, value)
	}
	if value, ok := acoaduo.mutation.EndTime(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldEndTime, field.TypeTime, value)
	}
	if acoaduo.mutation.EndTimeCleared() {
		_spec.ClearField(alicloudoriginattackdata.FieldEndTime, field.TypeTime)
	}
	if value, ok := acoaduo.mutation.Duration(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldDuration, field.TypeInt, value)
	}
	if value, ok := acoaduo.mutation.AddedDuration(); ok {
		_spec.AddField(alicloudoriginattackdata.FieldDuration, field.TypeInt, value)
	}
	if acoaduo.mutation.DurationCleared() {
		_spec.ClearField(alicloudoriginattackdata.FieldDuration, field.TypeInt)
	}
	if value, ok := acoaduo.mutation.Pps(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldPps, field.TypeInt64, value)
	}
	if value, ok := acoaduo.mutation.AddedPps(); ok {
		_spec.AddField(alicloudoriginattackdata.FieldPps, field.TypeInt64, value)
	}
	if value, ok := acoaduo.mutation.Mbps(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldMbps, field.TypeInt64, value)
	}
	if value, ok := acoaduo.mutation.AddedMbps(); ok {
		_spec.AddField(alicloudoriginattackdata.FieldMbps, field.TypeInt64, value)
	}
	if value, ok := acoaduo.mutation.Status(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldStatus, field.TypeString, value)
	}
	if acoaduo.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   alicloudoriginattackdata.TenantTable,
			Columns: []string{alicloudoriginattackdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := acoaduo.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   alicloudoriginattackdata.TenantTable,
			Columns: []string{alicloudoriginattackdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &AliCloudOriginAttackData{config: acoaduo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, acoaduo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{alicloudoriginattackdata.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	acoaduo.mutation.done = true
	return _node, nil
}
