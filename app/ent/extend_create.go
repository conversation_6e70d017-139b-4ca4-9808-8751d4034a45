// Code generated by ent, DO NOT EDIT.

package ent

// SetAliCloudOriginAttackData 设置 AliCloudOriginAttackData 值
func (acoadc *AliCloudOriginAttackDataCreate) SetItemAliCloudOriginAttackData(input *AliCloudOriginAttackData) *AliCloudOriginAttackDataCreate {
	acoadc.SetNillableTenantID(input.TenantID)

	if !input.CreatedAt.IsZero() {
		acoadc.SetCreatedAt(input.CreatedAt)
	}

	if !input.UpdatedAt.IsZero() {
		acoadc.SetUpdatedAt(input.UpdatedAt)
	}

	acoadc.SetNillableRemark(input.Remark)

	acoadc.SetEventType(input.EventType)

	acoadc.SetProject(input.Project)

	acoadc.SetIP(input.IP)

	acoadc.SetRegion(input.Region)

	acoadc.SetAction(input.Action)

	if !input.Time.IsZero() {
		acoadc.SetTime(input.Time)
	}

	acoadc.SetPps(input.Pps)

	acoadc.SetBps(input.Bps)

	acoadc.SetDdosType(input.DdosType)

	return acoadc
}

// SetCasbinRule 设置 CasbinRule 值
func (crc *CasbinRuleCreate) SetItemCasbinRule(input *CasbinRule) *CasbinRuleCreate {
	crc.SetType(input.Type)

	crc.SetSub(input.Sub)

	crc.SetDom(input.Dom)

	crc.SetObj(input.Obj)

	crc.SetAct(input.Act)

	crc.SetV4(input.V4)

	crc.SetV5(input.V5)

	return crc
}

// SetCleanData 设置 CleanData 值
func (cdc *CleanDataCreate) SetItemCleanData(input *CleanData) *CleanDataCreate {
	cdc.SetNillableTenantID(input.TenantID)

	if !input.CreatedAt.IsZero() {
		cdc.SetCreatedAt(input.CreatedAt)
	}

	cdc.SetNillableSpectrumAlertID(input.SpectrumAlertID)

	cdc.SetIP(input.IP)

	if !input.Time.IsZero() {
		cdc.SetTime(input.Time)
	}

	cdc.SetInBps(input.InBps)

	cdc.SetOutBps(input.OutBps)

	cdc.SetInPps(input.InPps)

	cdc.SetOutPps(input.OutPps)

	cdc.SetInAckPps(input.InAckPps)

	cdc.SetOutAckPps(input.OutAckPps)

	cdc.SetInAckBps(input.InAckBps)

	cdc.SetOutAckBps(input.OutAckBps)

	cdc.SetInSynPps(input.InSynPps)

	cdc.SetOutSynPps(input.OutSynPps)

	cdc.SetInUDPPps(input.InUDPPps)

	cdc.SetOutUDPPps(input.OutUDPPps)

	cdc.SetInUDPBps(input.InUDPBps)

	cdc.SetOutUDPBps(input.OutUDPBps)

	cdc.SetInIcmpPps(input.InIcmpPps)

	cdc.SetInIcmpBps(input.InIcmpBps)

	cdc.SetOutIcmpBps(input.OutIcmpBps)

	cdc.SetOutIcmpPps(input.OutIcmpPps)

	cdc.SetInDNSPps(input.InDNSPps)

	cdc.SetOutDNSPps(input.OutDNSPps)

	cdc.SetInDNSBps(input.InDNSBps)

	cdc.SetOutDNSBps(input.OutDNSBps)

	cdc.SetCFilterID(input.CFilterID)

	cdc.SetAttackFlags(input.AttackFlags)

	cdc.SetCount(input.Count)

	cdc.SetIPType(input.IPType)

	cdc.SetNillableCFilter(input.CFilter)

	cdc.SetNillableHost(input.Host)

	return cdc
}

// SetCloudAlert 设置 CloudAlert 值
func (cac *CloudAlertCreate) SetItemCloudAlert(input *CloudAlert) *CloudAlertCreate {
	cac.SetNillableTenantID(input.TenantID)

	if !input.CreatedAt.IsZero() {
		cac.SetCreatedAt(input.CreatedAt)
	}

	if !input.UpdatedAt.IsZero() {
		cac.SetUpdatedAt(input.UpdatedAt)
	}

	cac.SetNillableRemark(input.Remark)

	cac.SetSrcIP(input.SrcIP)

	cac.SetSrcPort(input.SrcPort)

	cac.SetDstIP(input.DstIP)

	cac.SetDstPort(input.DstPort)

	cac.SetDefenceMode(input.DefenceMode)

	cac.SetFlowMode(input.FlowMode)

	cac.SetTCPAckNum(input.TCPAckNum)

	cac.SetTCPSeqNum(input.TCPSeqNum)

	cac.SetProtocol(input.Protocol)

	cac.SetDefenceLevel(input.DefenceLevel)

	cac.SetMaxPps(input.MaxPps)

	cac.SetMaxAttackPps(input.MaxAttackPps)

	cac.SetOverlimitPktCount(input.OverlimitPktCount)

	if !input.StartTime.IsZero() {
		cac.SetStartTime(input.StartTime)
	}

	if !input.EndTime.IsZero() {
		cac.SetEndTime(input.EndTime)
	}

	return cac
}

// SetCloudAttackData 设置 CloudAttackData 值
func (cadc *CloudAttackDataCreate) SetItemCloudAttackData(input *CloudAttackData) *CloudAttackDataCreate {
	cadc.SetNillableTenantID(input.TenantID)

	if !input.CreatedAt.IsZero() {
		cadc.SetCreatedAt(input.CreatedAt)
	}

	if !input.UpdatedAt.IsZero() {
		cadc.SetUpdatedAt(input.UpdatedAt)
	}

	cadc.SetNillableRemark(input.Remark)

	cadc.SetSrcIP(input.SrcIP)

	cadc.SetSrcPort(input.SrcPort)

	cadc.SetDstIP(input.DstIP)

	cadc.SetDstPort(input.DstPort)

	cadc.SetProtocol(input.Protocol)

	cadc.SetCurrentAttackPps(input.CurrentAttackPps)

	if !input.StartTime.IsZero() {
		cadc.SetStartTime(input.StartTime)
	}

	if !input.EndTime.IsZero() {
		cadc.SetEndTime(input.EndTime)
	}

	return cadc
}

// SetCloudFlowData 设置 CloudFlowData 值
func (cfdc *CloudFlowDataCreate) SetItemCloudFlowData(input *CloudFlowData) *CloudFlowDataCreate {
	cfdc.SetNillableTenantID(input.TenantID)

	if !input.CreatedAt.IsZero() {
		cfdc.SetCreatedAt(input.CreatedAt)
	}

	if !input.UpdatedAt.IsZero() {
		cfdc.SetUpdatedAt(input.UpdatedAt)
	}

	cfdc.SetNillableRemark(input.Remark)

	cfdc.SetNillableCloudAlertID(input.CloudAlertID)

	cfdc.SetSrcIP(input.SrcIP)

	cfdc.SetSrcPort(input.SrcPort)

	cfdc.SetDstIP(input.DstIP)

	cfdc.SetDstPort(input.DstPort)

	cfdc.SetProtocol(input.Protocol)

	cfdc.SetMaxAttackPps(input.MaxAttackPps)

	cfdc.SetFlowOverMaxPpsCount(input.FlowOverMaxPpsCount)

	if !input.StartTime.IsZero() {
		cfdc.SetStartTime(input.StartTime)
	}

	if !input.EndTime.IsZero() {
		cfdc.SetEndTime(input.EndTime)
	}

	return cfdc
}

// SetDataSync 设置 DataSync 值
func (dsc *DataSyncCreate) SetItemDataSync(input *DataSync) *DataSyncCreate {
	if !input.CreatedAt.IsZero() {
		dsc.SetCreatedAt(input.CreatedAt)
	}

	if !input.UpdatedAt.IsZero() {
		dsc.SetUpdatedAt(input.UpdatedAt)
	}

	dsc.SetNillableRemark(input.Remark)

	dsc.SetPreDataList(input.PreDataList)

	dsc.SetDataList(input.DataList)

	dsc.SetDataType(input.DataType)

	dsc.SetType(input.Type)

	return dsc
}

// SetGroup 设置 Group 值
func (gc *GroupCreate) SetItemGroup(input *Group) *GroupCreate {
	gc.SetNillableTenantID(input.TenantID)

	gc.SetName(input.Name)

	return gc
}

// SetMatrixSpectrumAlert 设置 MatrixSpectrumAlert 值
func (msac *MatrixSpectrumAlertCreate) SetItemMatrixSpectrumAlert(input *MatrixSpectrumAlert) *MatrixSpectrumAlertCreate {
	msac.SetNillableTenantID(input.TenantID)

	if !input.CreatedAt.IsZero() {
		msac.SetCreatedAt(input.CreatedAt)
	}

	if !input.UpdatedAt.IsZero() {
		msac.SetUpdatedAt(input.UpdatedAt)
	}

	msac.SetNillableRemark(input.Remark)

	msac.SetNillableWofangID(input.WofangID)

	msac.SetNillableMatrixStrategyID(input.MatrixStrategyID)

	msac.SetIPList(input.IPList)

	msac.SetRegion(input.Region)

	msac.SetNetType(input.NetType)

	msac.SetIsp(input.Isp)

	if !input.StartTime.IsZero() {
		msac.SetStartTime(input.StartTime)
	}

	if !input.EndTime.IsZero() {
		msac.SetEndTime(input.EndTime)
	}

	msac.SetAttackType(input.AttackType)

	msac.SetBps(input.Bps)

	msac.SetAttackInfo(input.AttackInfo)

	return msac
}

// SetMatrixSpectrumData 设置 MatrixSpectrumData 值
func (msdc *MatrixSpectrumDataCreate) SetItemMatrixSpectrumData(input *MatrixSpectrumData) *MatrixSpectrumDataCreate {
	msdc.SetNillableTenantID(input.TenantID)

	if !input.CreatedAt.IsZero() {
		msdc.SetCreatedAt(input.CreatedAt)
	}

	if !input.UpdatedAt.IsZero() {
		msdc.SetUpdatedAt(input.UpdatedAt)
	}

	msdc.SetNillableMatrixSpectrumAlertID(input.MatrixSpectrumAlertID)

	msdc.SetRegion(input.Region)

	msdc.SetNetType(input.NetType)

	msdc.SetIsp(input.Isp)

	msdc.SetBps(input.Bps)

	if !input.Time.IsZero() {
		msdc.SetTime(input.Time)
	}

	return msdc
}

// SetMatrixStrategy 设置 MatrixStrategy 值
func (msc *MatrixStrategyCreate) SetItemMatrixStrategy(input *MatrixStrategy) *MatrixStrategyCreate {
	if !input.CreatedAt.IsZero() {
		msc.SetCreatedAt(input.CreatedAt)
	}

	if !input.UpdatedAt.IsZero() {
		msc.SetUpdatedAt(input.UpdatedAt)
	}

	msc.SetNillableRemark(input.Remark)

	msc.SetName(input.Name)

	msc.SetRegion(input.Region)

	msc.SetNetType(input.NetType)

	msc.SetIsp(input.Isp)

	msc.SetMonitorBps(input.MonitorBps)

	msc.SetDragBps(input.DragBps)

	msc.SetDragType(input.DragType)

	return msc
}

// SetNotify 设置 Notify 值
func (nc *NotifyCreate) SetItemNotify(input *Notify) *NotifyCreate {
	if !input.CreatedAt.IsZero() {
		nc.SetCreatedAt(input.CreatedAt)
	}

	if !input.UpdatedAt.IsZero() {
		nc.SetUpdatedAt(input.UpdatedAt)
	}

	nc.SetNillableTenantID(input.TenantID)

	nc.SetNillableRemark(input.Remark)

	nc.SetName(input.Name)

	nc.SetPopo(input.Popo)

	nc.SetEmail(input.Email)

	nc.SetSms(input.Sms)

	nc.SetPhone(input.Phone)

	nc.SetPopoGroups(input.PopoGroups)

	nc.SetEmails(input.Emails)

	nc.SetPhones(input.Phones)

	nc.SetIPWhitelists(input.IPWhitelists)

	nc.SetSystem(input.System)

	nc.SetEnabled(input.Enabled)

	nc.SetSaNotifyPopo(input.SaNotifyPopo)

	nc.SetSaNotifyEmail(input.SaNotifyEmail)

	return nc
}

// SetProtectGroup 设置 ProtectGroup 值
func (pgc *ProtectGroupCreate) SetItemProtectGroup(input *ProtectGroup) *ProtectGroupCreate {
	pgc.SetNillableTenantID(input.TenantID)

	if !input.CreatedAt.IsZero() {
		pgc.SetCreatedAt(input.CreatedAt)
	}

	if !input.UpdatedAt.IsZero() {
		pgc.SetUpdatedAt(input.UpdatedAt)
	}

	pgc.SetNillableRemark(input.Remark)

	pgc.SetGroupName(input.GroupName)

	pgc.SetGroupID(input.GroupID)

	pgc.SetType(input.Type)

	pgc.SetIPList(input.IPList)

	pgc.SetExpandIP(input.ExpandIP)

	pgc.SetMonitorInfo(input.MonitorInfo)

	pgc.SetDragInfo(input.DragInfo)

	pgc.SetNds4Config(input.Nds4Config)

	pgc.SetNds6Config(input.Nds6Config)

	return pgc
}

// SetSkylineDos 设置 SkylineDos 值
func (sdc *SkylineDosCreate) SetItemSkylineDos(input *SkylineDos) *SkylineDosCreate {
	sdc.SetNillableTenantID(input.TenantID)

	if !input.CreatedAt.IsZero() {
		sdc.SetCreatedAt(input.CreatedAt)
	}

	if !input.UpdatedAt.IsZero() {
		sdc.SetUpdatedAt(input.UpdatedAt)
	}

	sdc.SetNillableRemark(input.Remark)

	if !input.StartTime.IsZero() {
		sdc.SetStartTime(input.StartTime)
	}

	if !input.EndTime.IsZero() {
		sdc.SetEndTime(input.EndTime)
	}

	sdc.SetRegion(input.Region)

	sdc.SetResource(input.Resource)

	sdc.SetResourceType(input.ResourceType)

	sdc.SetVectorTypes(input.VectorTypes)

	sdc.SetStatus(input.Status)

	sdc.SetAttackID(input.AttackID)

	sdc.SetAttackCounters(input.AttackCounters)

	sdc.SetProject(input.Project)

	sdc.SetDurationTime(input.DurationTime)

	return sdc
}

// SetSocGroupTicket 设置 SocGroupTicket 值
func (sgtc *SocGroupTicketCreate) SetItemSocGroupTicket(input *SocGroupTicket) *SocGroupTicketCreate {
	if !input.CreatedAt.IsZero() {
		sgtc.SetCreatedAt(input.CreatedAt)
	}

	if !input.UpdatedAt.IsZero() {
		sgtc.SetUpdatedAt(input.UpdatedAt)
	}

	sgtc.SetNillableTenantID(input.TenantID)

	sgtc.SetNillableRemark(input.Remark)

	sgtc.SetName(input.Name)

	sgtc.SetType(input.Type)

	sgtc.SetDescription(input.Description)

	sgtc.SetFollowList(input.FollowList)

	sgtc.SetDepartmentID(input.DepartmentID)

	sgtc.SetIPList(input.IPList)

	sgtc.SetMinBandwidth(input.MinBandwidth)

	sgtc.SetDivertType(input.DivertType)

	sgtc.SetOpType(input.OpType)

	if !input.OpTime.IsZero() {
		sgtc.SetOpTime(input.OpTime)
	}

	sgtc.SetConfigType(input.ConfigType)

	sgtc.SetConfigArgs(input.ConfigArgs)

	sgtc.SetProductName(input.ProductName)

	sgtc.SetProductCode(input.ProductCode)

	sgtc.SetContactList(input.ContactList)

	sgtc.SetGroupTicketID(input.GroupTicketID)

	sgtc.SetErrorInfo(input.ErrorInfo)

	sgtc.SetNillableCreateUserID(input.CreateUserID)

	return sgtc
}

// SetSpectrumAlert 设置 SpectrumAlert 值
func (sac *SpectrumAlertCreate) SetItemSpectrumAlert(input *SpectrumAlert) *SpectrumAlertCreate {
	sac.SetNillableTenantID(input.TenantID)

	if !input.CreatedAt.IsZero() {
		sac.SetCreatedAt(input.CreatedAt)
	}

	if !input.UpdatedAt.IsZero() {
		sac.SetUpdatedAt(input.UpdatedAt)
	}

	sac.SetNillableRemark(input.Remark)

	sac.SetNillableProtectGroupID(input.ProtectGroupID)

	sac.SetNillableStrategyID(input.StrategyID)

	sac.SetNillableWofangID(input.WofangID)

	sac.SetProtectStatus(input.ProtectStatus)

	sac.SetIP(input.IP)

	if !input.StartTime.IsZero() {
		sac.SetStartTime(input.StartTime)
	}

	if !input.EndTime.IsZero() {
		sac.SetEndTime(input.EndTime)
	}

	sac.SetAttackType(input.AttackType)

	sac.SetMaxPps(input.MaxPps)

	sac.SetMaxBps(input.MaxBps)

	sac.SetAttackInfo(input.AttackInfo)

	sac.SetIspCode(input.IspCode)

	return sac
}

// SetSpectrumData 设置 SpectrumData 值
func (sdc *SpectrumDataCreate) SetItemSpectrumData(input *SpectrumData) *SpectrumDataCreate {
	sdc.SetNillableTenantID(input.TenantID)

	if !input.CreatedAt.IsZero() {
		sdc.SetCreatedAt(input.CreatedAt)
	}

	sdc.SetNillableSpectrumAlertID(input.SpectrumAlertID)

	sdc.SetIP(input.IP)

	if !input.Time.IsZero() {
		sdc.SetTime(input.Time)
	}

	sdc.SetMonitorID(input.MonitorID)

	sdc.SetDataType(input.DataType)

	sdc.SetBps(input.Bps)

	sdc.SetPps(input.Pps)

	sdc.SetSynBps(input.SynBps)

	sdc.SetSynPps(input.SynPps)

	sdc.SetAckBps(input.AckBps)

	sdc.SetAckPps(input.AckPps)

	sdc.SetSynAckBps(input.SynAckBps)

	sdc.SetSynAckPps(input.SynAckPps)

	sdc.SetIcmpBps(input.IcmpBps)

	sdc.SetIcmpPps(input.IcmpPps)

	sdc.SetSmallPps(input.SmallPps)

	sdc.SetNtpPps(input.NtpPps)

	sdc.SetNtpBps(input.NtpBps)

	sdc.SetDNSQueryPps(input.DNSQueryPps)

	sdc.SetDNSQueryBps(input.DNSQueryBps)

	sdc.SetDNSAnswerPps(input.DNSAnswerPps)

	sdc.SetDNSAnswerBps(input.DNSAnswerBps)

	sdc.SetSsdpBps(input.SsdpBps)

	sdc.SetSsdpPps(input.SsdpPps)

	sdc.SetUDPPps(input.UDPPps)

	sdc.SetUDPBps(input.UDPBps)

	sdc.SetQPS(input.QPS)

	sdc.SetReceiveCount(input.ReceiveCount)

	sdc.SetIPType(input.IPType)

	sdc.SetNillableMonitor(input.Monitor)

	sdc.SetNillableProduct(input.Product)

	sdc.SetNillableHost(input.Host)

	return sdc
}

// SetStrategy 设置 Strategy 值
func (sc *StrategyCreate) SetItemStrategy(input *Strategy) *StrategyCreate {
	sc.SetNillableTenantID(input.TenantID)

	if !input.CreatedAt.IsZero() {
		sc.SetCreatedAt(input.CreatedAt)
	}

	if !input.UpdatedAt.IsZero() {
		sc.SetUpdatedAt(input.UpdatedAt)
	}

	sc.SetNillableRemark(input.Remark)

	sc.SetName(input.Name)

	sc.SetType(input.Type)

	sc.SetEnabled(input.Enabled)

	sc.SetSystem(input.System)

	sc.SetBps(input.Bps)

	sc.SetPps(input.Pps)

	sc.SetBpsCount(input.BpsCount)

	sc.SetPpsCount(input.PpsCount)

	sc.SetIspCode(input.IspCode)

	return sc
}

// SetSystemApi 设置 SystemApi 值
func (sac *SystemApiCreate) SetItemSystemApi(input *SystemApi) *SystemApiCreate {
	if !input.CreatedAt.IsZero() {
		sac.SetCreatedAt(input.CreatedAt)
	}

	if !input.UpdatedAt.IsZero() {
		sac.SetUpdatedAt(input.UpdatedAt)
	}

	sac.SetNillableRemark(input.Remark)

	sac.SetName(input.Name)

	sac.SetPath(input.Path)

	sac.SetHTTPMethod(input.HTTPMethod)

	sac.SetRoles(input.Roles)

	sac.SetPublic(input.Public)

	sac.SetSa(input.Sa)

	return sac
}

// SetSystemConfig 设置 SystemConfig 值
func (scc *SystemConfigCreate) SetItemSystemConfig(input *SystemConfig) *SystemConfigCreate {
	if !input.CreatedAt.IsZero() {
		scc.SetCreatedAt(input.CreatedAt)
	}

	if !input.UpdatedAt.IsZero() {
		scc.SetUpdatedAt(input.UpdatedAt)
	}

	scc.SetNillableRemark(input.Remark)

	scc.SetWofangTestIP(input.WofangTestIP)

	scc.SetNotifyPhones(input.NotifyPhones)

	scc.SetNotifyEmails(input.NotifyEmails)

	scc.SetNotifyScenes(input.NotifyScenes)

	scc.SetIPWhitelists(input.IPWhitelists)

	return scc
}

// SetTenant 设置 Tenant 值
func (tc *TenantCreate) SetItemTenant(input *Tenant) *TenantCreate {
	tc.SetName(input.Name)

	tc.SetCode(input.Code)

	tc.SetOffline(input.Offline)

	return tc
}

// SetUser 设置 User 值
func (uc *UserCreate) SetItemUser(input *User) *UserCreate {
	uc.SetValid(input.Valid)

	if !input.CreatedAt.IsZero() {
		uc.SetCreatedAt(input.CreatedAt)
	}

	if !input.UpdatedAt.IsZero() {
		uc.SetUpdatedAt(input.UpdatedAt)
	}

	uc.SetName(input.Name)

	uc.SetPassword(input.Password)

	uc.SetSuperAdmin(input.SuperAdmin)

	uc.SetUpdateAuth(input.UpdateAuth)

	return uc
}

// SetUserOperationLog 设置 UserOperationLog 值
func (uolc *UserOperationLogCreate) SetItemUserOperationLog(input *UserOperationLog) *UserOperationLogCreate {
	uolc.SetNillableRemark(input.Remark)

	if !input.CreatedAt.IsZero() {
		uolc.SetCreatedAt(input.CreatedAt)
	}

	if !input.UpdatedAt.IsZero() {
		uolc.SetUpdatedAt(input.UpdatedAt)
	}

	uolc.SetUsername(input.Username)

	uolc.SetMethod(input.Method)

	uolc.SetRequestID(input.RequestID)

	uolc.SetURI(input.URI)

	uolc.SetRequestBody(input.RequestBody)

	uolc.SetProject(input.Project)

	return uolc
}

// SetWofang 设置 Wofang 值
func (wc *WofangCreate) SetItemWofang(input *Wofang) *WofangCreate {
	if !input.CreatedAt.IsZero() {
		wc.SetCreatedAt(input.CreatedAt)
	}

	if !input.UpdatedAt.IsZero() {
		wc.SetUpdatedAt(input.UpdatedAt)
	}

	wc.SetNillableTenantID(input.TenantID)

	wc.SetNillableRemark(input.Remark)

	wc.SetName(input.Name)

	wc.SetIP(input.IP)

	wc.SetType(input.Type)

	wc.SetUnDragSecond(input.UnDragSecond)

	if !input.StartTime.IsZero() {
		wc.SetStartTime(input.StartTime)
	}

	wc.SetErrorInfo(input.ErrorInfo)

	wc.SetStatus(input.Status)

	wc.SetNillableCreateUserID(input.CreateUserID)

	return wc
}

// SetWofangAlert 设置 WofangAlert 值
func (wac *WofangAlertCreate) SetItemWofangAlert(input *WofangAlert) *WofangAlertCreate {
	wac.SetNillableTenantID(input.TenantID)

	if !input.CreatedAt.IsZero() {
		wac.SetCreatedAt(input.CreatedAt)
	}

	if !input.UpdatedAt.IsZero() {
		wac.SetUpdatedAt(input.UpdatedAt)
	}

	wac.SetNillableRemark(input.Remark)

	wac.SetAttackStatus(input.AttackStatus)

	wac.SetAttackType(input.AttackType)

	wac.SetDeviceIP(input.DeviceIP)

	wac.SetZoneIP(input.ZoneIP)

	wac.SetAttackID(input.AttackID)

	if !input.StartTime.IsZero() {
		wac.SetStartTime(input.StartTime)
	}

	if !input.EndTime.IsZero() {
		wac.SetEndTime(input.EndTime)
	}

	wac.SetMaxDropBps(input.MaxDropBps)

	wac.SetMaxInBps(input.MaxInBps)

	return wac
}
