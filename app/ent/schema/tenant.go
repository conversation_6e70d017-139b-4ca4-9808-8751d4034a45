package schema

import (
	"meta/app/ent/privacy"
	"meta/pkg/ent/rule"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
)

// Tenant holds the schema definition for the Tenant entity.
type Tenant struct {
	ent.Schema
}

// Mixin of the Tenant schema.
func (Tenant) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
	}
}

// Fields of the Tenant.
func (Tenant) Fields() []ent.Field {
	return []ent.Field{
		field.String("name").NotEmpty().StructTag(`redis:"name"`),
		field.String("code").NotEmpty().Unique().StructTag(`redis:"code"`),
		field.Bool("offline").StructTag(`redis:"offline"`),

		// field.Bool("saNotifyPopo").Comment("true，通过auth获取sa列表，并通过popo进行通知"), field.Bool("saNotifyEmail").Comment("true，通过auth获取sa列表，并通过邮件进行通知"),
		// field.Bool("notifyPopo").Comment("true，通过popo对notifyEmails进行通知"),
		// field.Bool("notifyEmail").Comment("true，通过邮件对notifyEmails进行通知"),
		// field.JSON("notifyPopoGroups", []string{}).Comment("通过popo群进行通知"),
		// field.JSON("notifyEmails", []string{}),
	}
}

// Edges of the Tenant.
func (Tenant) Edges() []ent.Edge {
	// return []ent.Edge{
	// 	edge.To("users", User.NetType),
	// }
	return nil
}

// Policy defines the privacy policy of the User.
func (Tenant) Policy() ent.Policy {
	return privacy.Policy{
		Mutation: privacy.MutationPolicy{
			// For Tenant type, we only allow admin users to mutate
			// the tenant information and deny otherwise.
			rule.AllowIfAdmin(),
			privacy.AlwaysDenyRule(),
		},
	}
}
