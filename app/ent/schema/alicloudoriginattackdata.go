package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/field"
)

// AliCloudOriginAttackData holds the schema definition for the AliCloudOriginAttackData entity.
type AliCloudOriginAttackData struct {
	ent.Schema
}

// Mixin of the AliCloudOriginAttackData.
func (AliCloudOriginAttackData) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
		TenantMixin{},
		TimeMixin{},
		RemarkMixin{},
	}
}

// Fields of the AliCloudOriginAttackData.
func (AliCloudOriginAttackData) Fields() []ent.Field {
	return []ent.Field{
		field.String("event_type").Comment("事件类型").StructTag(`query:"event_type,omitempty"`),
		field.String("project").Comment("IP 所属项目").StructTag(`query:"project,omitempty"`),
		field.String("ip").Comment("被攻击IP").StructTag(`query:"ip,omitempty"`),
		field.String("region").Comment("区域").StructTag(`query:"region,omitempty"`),
		field.String("action").Comment("动作：add 事件开始; del 事件结束").StructTag(`query:"action,omitempty"`),
		field.Time("time").Comment("事件时间"),
		field.Int64("pps").Comment("报文数量大小").StructTag(`query:"pps,omitempty"`),
		field.Int64("bps").Comment("流量大小").StructTag(`query:"bps,omitempty"`),
		field.String("ddos_type").Comment("攻击事件类型：defense 清洗; blackhole 黑洞").StructTag(`query:"ddos_type,omitempty"`),
	}
}

// Edges of the AliCloudOriginAttackData.
func (AliCloudOriginAttackData) Edges() []ent.Edge {
	return nil
}
