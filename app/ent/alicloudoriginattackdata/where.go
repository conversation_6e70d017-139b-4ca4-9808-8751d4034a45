// Code generated by ent, DO NOT EDIT.

package alicloudoriginattackdata

import (
	"meta/app/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLTE(FieldID, id))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldTenantID, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldUpdatedAt, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldRemark, v))
}

// EventType applies equality check predicate on the "event_type" field. It's identical to EventTypeEQ.
func EventType(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldEventType, v))
}

// Project applies equality check predicate on the "project" field. It's identical to ProjectEQ.
func Project(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldProject, v))
}

// IP applies equality check predicate on the "ip" field. It's identical to IPEQ.
func IP(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldIP, v))
}

// Region applies equality check predicate on the "region" field. It's identical to RegionEQ.
func Region(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldRegion, v))
}

// Action applies equality check predicate on the "action" field. It's identical to ActionEQ.
func Action(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldAction, v))
}

// Time applies equality check predicate on the "time" field. It's identical to TimeEQ.
func Time(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldTime, v))
}

// Pps applies equality check predicate on the "pps" field. It's identical to PpsEQ.
func Pps(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldPps, v))
}

// Bps applies equality check predicate on the "bps" field. It's identical to BpsEQ.
func Bps(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldBps, v))
}

// DdosType applies equality check predicate on the "ddos_type" field. It's identical to DdosTypeEQ.
func DdosType(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldDdosType, v))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDIsNil applies the IsNil predicate on the "tenant_id" field.
func TenantIDIsNil() predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIsNull(FieldTenantID))
}

// TenantIDNotNil applies the NotNil predicate on the "tenant_id" field.
func TenantIDNotNil() predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotNull(FieldTenantID))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLTE(FieldUpdatedAt, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldContainsFold(FieldRemark, v))
}

// EventTypeEQ applies the EQ predicate on the "event_type" field.
func EventTypeEQ(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldEventType, v))
}

// EventTypeNEQ applies the NEQ predicate on the "event_type" field.
func EventTypeNEQ(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldEventType, v))
}

// EventTypeIn applies the In predicate on the "event_type" field.
func EventTypeIn(vs ...string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldEventType, vs...))
}

// EventTypeNotIn applies the NotIn predicate on the "event_type" field.
func EventTypeNotIn(vs ...string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldEventType, vs...))
}

// EventTypeGT applies the GT predicate on the "event_type" field.
func EventTypeGT(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGT(FieldEventType, v))
}

// EventTypeGTE applies the GTE predicate on the "event_type" field.
func EventTypeGTE(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGTE(FieldEventType, v))
}

// EventTypeLT applies the LT predicate on the "event_type" field.
func EventTypeLT(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLT(FieldEventType, v))
}

// EventTypeLTE applies the LTE predicate on the "event_type" field.
func EventTypeLTE(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLTE(FieldEventType, v))
}

// EventTypeContains applies the Contains predicate on the "event_type" field.
func EventTypeContains(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldContains(FieldEventType, v))
}

// EventTypeHasPrefix applies the HasPrefix predicate on the "event_type" field.
func EventTypeHasPrefix(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldHasPrefix(FieldEventType, v))
}

// EventTypeHasSuffix applies the HasSuffix predicate on the "event_type" field.
func EventTypeHasSuffix(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldHasSuffix(FieldEventType, v))
}

// EventTypeEqualFold applies the EqualFold predicate on the "event_type" field.
func EventTypeEqualFold(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEqualFold(FieldEventType, v))
}

// EventTypeContainsFold applies the ContainsFold predicate on the "event_type" field.
func EventTypeContainsFold(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldContainsFold(FieldEventType, v))
}

// ProjectEQ applies the EQ predicate on the "project" field.
func ProjectEQ(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldProject, v))
}

// ProjectNEQ applies the NEQ predicate on the "project" field.
func ProjectNEQ(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldProject, v))
}

// ProjectIn applies the In predicate on the "project" field.
func ProjectIn(vs ...string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldProject, vs...))
}

// ProjectNotIn applies the NotIn predicate on the "project" field.
func ProjectNotIn(vs ...string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldProject, vs...))
}

// ProjectGT applies the GT predicate on the "project" field.
func ProjectGT(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGT(FieldProject, v))
}

// ProjectGTE applies the GTE predicate on the "project" field.
func ProjectGTE(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGTE(FieldProject, v))
}

// ProjectLT applies the LT predicate on the "project" field.
func ProjectLT(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLT(FieldProject, v))
}

// ProjectLTE applies the LTE predicate on the "project" field.
func ProjectLTE(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLTE(FieldProject, v))
}

// ProjectContains applies the Contains predicate on the "project" field.
func ProjectContains(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldContains(FieldProject, v))
}

// ProjectHasPrefix applies the HasPrefix predicate on the "project" field.
func ProjectHasPrefix(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldHasPrefix(FieldProject, v))
}

// ProjectHasSuffix applies the HasSuffix predicate on the "project" field.
func ProjectHasSuffix(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldHasSuffix(FieldProject, v))
}

// ProjectEqualFold applies the EqualFold predicate on the "project" field.
func ProjectEqualFold(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEqualFold(FieldProject, v))
}

// ProjectContainsFold applies the ContainsFold predicate on the "project" field.
func ProjectContainsFold(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldContainsFold(FieldProject, v))
}

// IPEQ applies the EQ predicate on the "ip" field.
func IPEQ(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldIP, v))
}

// IPNEQ applies the NEQ predicate on the "ip" field.
func IPNEQ(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldIP, v))
}

// IPIn applies the In predicate on the "ip" field.
func IPIn(vs ...string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldIP, vs...))
}

// IPNotIn applies the NotIn predicate on the "ip" field.
func IPNotIn(vs ...string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldIP, vs...))
}

// IPGT applies the GT predicate on the "ip" field.
func IPGT(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGT(FieldIP, v))
}

// IPGTE applies the GTE predicate on the "ip" field.
func IPGTE(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGTE(FieldIP, v))
}

// IPLT applies the LT predicate on the "ip" field.
func IPLT(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLT(FieldIP, v))
}

// IPLTE applies the LTE predicate on the "ip" field.
func IPLTE(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLTE(FieldIP, v))
}

// IPContains applies the Contains predicate on the "ip" field.
func IPContains(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldContains(FieldIP, v))
}

// IPHasPrefix applies the HasPrefix predicate on the "ip" field.
func IPHasPrefix(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldHasPrefix(FieldIP, v))
}

// IPHasSuffix applies the HasSuffix predicate on the "ip" field.
func IPHasSuffix(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldHasSuffix(FieldIP, v))
}

// IPEqualFold applies the EqualFold predicate on the "ip" field.
func IPEqualFold(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEqualFold(FieldIP, v))
}

// IPContainsFold applies the ContainsFold predicate on the "ip" field.
func IPContainsFold(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldContainsFold(FieldIP, v))
}

// RegionEQ applies the EQ predicate on the "region" field.
func RegionEQ(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldRegion, v))
}

// RegionNEQ applies the NEQ predicate on the "region" field.
func RegionNEQ(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldRegion, v))
}

// RegionIn applies the In predicate on the "region" field.
func RegionIn(vs ...string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldRegion, vs...))
}

// RegionNotIn applies the NotIn predicate on the "region" field.
func RegionNotIn(vs ...string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldRegion, vs...))
}

// RegionGT applies the GT predicate on the "region" field.
func RegionGT(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGT(FieldRegion, v))
}

// RegionGTE applies the GTE predicate on the "region" field.
func RegionGTE(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGTE(FieldRegion, v))
}

// RegionLT applies the LT predicate on the "region" field.
func RegionLT(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLT(FieldRegion, v))
}

// RegionLTE applies the LTE predicate on the "region" field.
func RegionLTE(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLTE(FieldRegion, v))
}

// RegionContains applies the Contains predicate on the "region" field.
func RegionContains(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldContains(FieldRegion, v))
}

// RegionHasPrefix applies the HasPrefix predicate on the "region" field.
func RegionHasPrefix(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldHasPrefix(FieldRegion, v))
}

// RegionHasSuffix applies the HasSuffix predicate on the "region" field.
func RegionHasSuffix(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldHasSuffix(FieldRegion, v))
}

// RegionEqualFold applies the EqualFold predicate on the "region" field.
func RegionEqualFold(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEqualFold(FieldRegion, v))
}

// RegionContainsFold applies the ContainsFold predicate on the "region" field.
func RegionContainsFold(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldContainsFold(FieldRegion, v))
}

// ActionEQ applies the EQ predicate on the "action" field.
func ActionEQ(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldAction, v))
}

// ActionNEQ applies the NEQ predicate on the "action" field.
func ActionNEQ(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldAction, v))
}

// ActionIn applies the In predicate on the "action" field.
func ActionIn(vs ...string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldAction, vs...))
}

// ActionNotIn applies the NotIn predicate on the "action" field.
func ActionNotIn(vs ...string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldAction, vs...))
}

// ActionGT applies the GT predicate on the "action" field.
func ActionGT(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGT(FieldAction, v))
}

// ActionGTE applies the GTE predicate on the "action" field.
func ActionGTE(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGTE(FieldAction, v))
}

// ActionLT applies the LT predicate on the "action" field.
func ActionLT(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLT(FieldAction, v))
}

// ActionLTE applies the LTE predicate on the "action" field.
func ActionLTE(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLTE(FieldAction, v))
}

// ActionContains applies the Contains predicate on the "action" field.
func ActionContains(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldContains(FieldAction, v))
}

// ActionHasPrefix applies the HasPrefix predicate on the "action" field.
func ActionHasPrefix(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldHasPrefix(FieldAction, v))
}

// ActionHasSuffix applies the HasSuffix predicate on the "action" field.
func ActionHasSuffix(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldHasSuffix(FieldAction, v))
}

// ActionEqualFold applies the EqualFold predicate on the "action" field.
func ActionEqualFold(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEqualFold(FieldAction, v))
}

// ActionContainsFold applies the ContainsFold predicate on the "action" field.
func ActionContainsFold(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldContainsFold(FieldAction, v))
}

// TimeEQ applies the EQ predicate on the "time" field.
func TimeEQ(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldTime, v))
}

// TimeNEQ applies the NEQ predicate on the "time" field.
func TimeNEQ(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldTime, v))
}

// TimeIn applies the In predicate on the "time" field.
func TimeIn(vs ...time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldTime, vs...))
}

// TimeNotIn applies the NotIn predicate on the "time" field.
func TimeNotIn(vs ...time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldTime, vs...))
}

// TimeGT applies the GT predicate on the "time" field.
func TimeGT(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGT(FieldTime, v))
}

// TimeGTE applies the GTE predicate on the "time" field.
func TimeGTE(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGTE(FieldTime, v))
}

// TimeLT applies the LT predicate on the "time" field.
func TimeLT(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLT(FieldTime, v))
}

// TimeLTE applies the LTE predicate on the "time" field.
func TimeLTE(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLTE(FieldTime, v))
}

// PpsEQ applies the EQ predicate on the "pps" field.
func PpsEQ(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldPps, v))
}

// PpsNEQ applies the NEQ predicate on the "pps" field.
func PpsNEQ(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldPps, v))
}

// PpsIn applies the In predicate on the "pps" field.
func PpsIn(vs ...int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldPps, vs...))
}

// PpsNotIn applies the NotIn predicate on the "pps" field.
func PpsNotIn(vs ...int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldPps, vs...))
}

// PpsGT applies the GT predicate on the "pps" field.
func PpsGT(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGT(FieldPps, v))
}

// PpsGTE applies the GTE predicate on the "pps" field.
func PpsGTE(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGTE(FieldPps, v))
}

// PpsLT applies the LT predicate on the "pps" field.
func PpsLT(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLT(FieldPps, v))
}

// PpsLTE applies the LTE predicate on the "pps" field.
func PpsLTE(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLTE(FieldPps, v))
}

// BpsEQ applies the EQ predicate on the "bps" field.
func BpsEQ(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldBps, v))
}

// BpsNEQ applies the NEQ predicate on the "bps" field.
func BpsNEQ(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldBps, v))
}

// BpsIn applies the In predicate on the "bps" field.
func BpsIn(vs ...int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldBps, vs...))
}

// BpsNotIn applies the NotIn predicate on the "bps" field.
func BpsNotIn(vs ...int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldBps, vs...))
}

// BpsGT applies the GT predicate on the "bps" field.
func BpsGT(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGT(FieldBps, v))
}

// BpsGTE applies the GTE predicate on the "bps" field.
func BpsGTE(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGTE(FieldBps, v))
}

// BpsLT applies the LT predicate on the "bps" field.
func BpsLT(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLT(FieldBps, v))
}

// BpsLTE applies the LTE predicate on the "bps" field.
func BpsLTE(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLTE(FieldBps, v))
}

// DdosTypeEQ applies the EQ predicate on the "ddos_type" field.
func DdosTypeEQ(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldDdosType, v))
}

// DdosTypeNEQ applies the NEQ predicate on the "ddos_type" field.
func DdosTypeNEQ(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldDdosType, v))
}

// DdosTypeIn applies the In predicate on the "ddos_type" field.
func DdosTypeIn(vs ...string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldDdosType, vs...))
}

// DdosTypeNotIn applies the NotIn predicate on the "ddos_type" field.
func DdosTypeNotIn(vs ...string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldDdosType, vs...))
}

// DdosTypeGT applies the GT predicate on the "ddos_type" field.
func DdosTypeGT(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGT(FieldDdosType, v))
}

// DdosTypeGTE applies the GTE predicate on the "ddos_type" field.
func DdosTypeGTE(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGTE(FieldDdosType, v))
}

// DdosTypeLT applies the LT predicate on the "ddos_type" field.
func DdosTypeLT(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLT(FieldDdosType, v))
}

// DdosTypeLTE applies the LTE predicate on the "ddos_type" field.
func DdosTypeLTE(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLTE(FieldDdosType, v))
}

// DdosTypeContains applies the Contains predicate on the "ddos_type" field.
func DdosTypeContains(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldContains(FieldDdosType, v))
}

// DdosTypeHasPrefix applies the HasPrefix predicate on the "ddos_type" field.
func DdosTypeHasPrefix(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldHasPrefix(FieldDdosType, v))
}

// DdosTypeHasSuffix applies the HasSuffix predicate on the "ddos_type" field.
func DdosTypeHasSuffix(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldHasSuffix(FieldDdosType, v))
}

// DdosTypeEqualFold applies the EqualFold predicate on the "ddos_type" field.
func DdosTypeEqualFold(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEqualFold(FieldDdosType, v))
}

// DdosTypeContainsFold applies the ContainsFold predicate on the "ddos_type" field.
func DdosTypeContainsFold(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldContainsFold(FieldDdosType, v))
}

// HasTenant applies the HasEdge predicate on the "tenant" edge.
func HasTenant() predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTenantWith applies the HasEdge predicate on the "tenant" edge with a given conditions (other predicates).
func HasTenantWith(preds ...predicate.Tenant) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(func(s *sql.Selector) {
		step := newTenantStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.AliCloudOriginAttackData) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.AliCloudOriginAttackData) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.AliCloudOriginAttackData) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.NotPredicates(p))
}
