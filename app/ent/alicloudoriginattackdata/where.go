// Code generated by ent, DO NOT EDIT.

package alicloudoriginattackdata

import (
	"meta/app/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLTE(FieldID, id))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldTenantID, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldUpdatedAt, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldRemark, v))
}

// Project applies equality check predicate on the "project" field. It's identical to ProjectEQ.
func Project(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldProject, v))
}

// CloudType applies equality check predicate on the "cloud_type" field. It's identical to CloudTypeEQ.
func CloudType(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldCloudType, v))
}

// IP applies equality check predicate on the "ip" field. It's identical to IPEQ.
func IP(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldIP, v))
}

// StartTime applies equality check predicate on the "start_time" field. It's identical to StartTimeEQ.
func StartTime(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldStartTime, v))
}

// EndTime applies equality check predicate on the "end_time" field. It's identical to EndTimeEQ.
func EndTime(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldEndTime, v))
}

// Duration applies equality check predicate on the "duration" field. It's identical to DurationEQ.
func Duration(v int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldDuration, v))
}

// Pps applies equality check predicate on the "pps" field. It's identical to PpsEQ.
func Pps(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldPps, v))
}

// Mbps applies equality check predicate on the "mbps" field. It's identical to MbpsEQ.
func Mbps(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldMbps, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldStatus, v))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDIsNil applies the IsNil predicate on the "tenant_id" field.
func TenantIDIsNil() predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIsNull(FieldTenantID))
}

// TenantIDNotNil applies the NotNil predicate on the "tenant_id" field.
func TenantIDNotNil() predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotNull(FieldTenantID))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLTE(FieldUpdatedAt, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldContainsFold(FieldRemark, v))
}

// ProjectEQ applies the EQ predicate on the "project" field.
func ProjectEQ(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldProject, v))
}

// ProjectNEQ applies the NEQ predicate on the "project" field.
func ProjectNEQ(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldProject, v))
}

// ProjectIn applies the In predicate on the "project" field.
func ProjectIn(vs ...string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldProject, vs...))
}

// ProjectNotIn applies the NotIn predicate on the "project" field.
func ProjectNotIn(vs ...string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldProject, vs...))
}

// ProjectGT applies the GT predicate on the "project" field.
func ProjectGT(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGT(FieldProject, v))
}

// ProjectGTE applies the GTE predicate on the "project" field.
func ProjectGTE(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGTE(FieldProject, v))
}

// ProjectLT applies the LT predicate on the "project" field.
func ProjectLT(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLT(FieldProject, v))
}

// ProjectLTE applies the LTE predicate on the "project" field.
func ProjectLTE(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLTE(FieldProject, v))
}

// ProjectContains applies the Contains predicate on the "project" field.
func ProjectContains(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldContains(FieldProject, v))
}

// ProjectHasPrefix applies the HasPrefix predicate on the "project" field.
func ProjectHasPrefix(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldHasPrefix(FieldProject, v))
}

// ProjectHasSuffix applies the HasSuffix predicate on the "project" field.
func ProjectHasSuffix(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldHasSuffix(FieldProject, v))
}

// ProjectEqualFold applies the EqualFold predicate on the "project" field.
func ProjectEqualFold(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEqualFold(FieldProject, v))
}

// ProjectContainsFold applies the ContainsFold predicate on the "project" field.
func ProjectContainsFold(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldContainsFold(FieldProject, v))
}

// CloudTypeEQ applies the EQ predicate on the "cloud_type" field.
func CloudTypeEQ(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldCloudType, v))
}

// CloudTypeNEQ applies the NEQ predicate on the "cloud_type" field.
func CloudTypeNEQ(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldCloudType, v))
}

// CloudTypeIn applies the In predicate on the "cloud_type" field.
func CloudTypeIn(vs ...string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldCloudType, vs...))
}

// CloudTypeNotIn applies the NotIn predicate on the "cloud_type" field.
func CloudTypeNotIn(vs ...string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldCloudType, vs...))
}

// CloudTypeGT applies the GT predicate on the "cloud_type" field.
func CloudTypeGT(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGT(FieldCloudType, v))
}

// CloudTypeGTE applies the GTE predicate on the "cloud_type" field.
func CloudTypeGTE(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGTE(FieldCloudType, v))
}

// CloudTypeLT applies the LT predicate on the "cloud_type" field.
func CloudTypeLT(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLT(FieldCloudType, v))
}

// CloudTypeLTE applies the LTE predicate on the "cloud_type" field.
func CloudTypeLTE(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLTE(FieldCloudType, v))
}

// CloudTypeContains applies the Contains predicate on the "cloud_type" field.
func CloudTypeContains(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldContains(FieldCloudType, v))
}

// CloudTypeHasPrefix applies the HasPrefix predicate on the "cloud_type" field.
func CloudTypeHasPrefix(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldHasPrefix(FieldCloudType, v))
}

// CloudTypeHasSuffix applies the HasSuffix predicate on the "cloud_type" field.
func CloudTypeHasSuffix(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldHasSuffix(FieldCloudType, v))
}

// CloudTypeEqualFold applies the EqualFold predicate on the "cloud_type" field.
func CloudTypeEqualFold(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEqualFold(FieldCloudType, v))
}

// CloudTypeContainsFold applies the ContainsFold predicate on the "cloud_type" field.
func CloudTypeContainsFold(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldContainsFold(FieldCloudType, v))
}

// IPEQ applies the EQ predicate on the "ip" field.
func IPEQ(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldIP, v))
}

// IPNEQ applies the NEQ predicate on the "ip" field.
func IPNEQ(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldIP, v))
}

// IPIn applies the In predicate on the "ip" field.
func IPIn(vs ...string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldIP, vs...))
}

// IPNotIn applies the NotIn predicate on the "ip" field.
func IPNotIn(vs ...string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldIP, vs...))
}

// IPGT applies the GT predicate on the "ip" field.
func IPGT(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGT(FieldIP, v))
}

// IPGTE applies the GTE predicate on the "ip" field.
func IPGTE(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGTE(FieldIP, v))
}

// IPLT applies the LT predicate on the "ip" field.
func IPLT(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLT(FieldIP, v))
}

// IPLTE applies the LTE predicate on the "ip" field.
func IPLTE(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLTE(FieldIP, v))
}

// IPContains applies the Contains predicate on the "ip" field.
func IPContains(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldContains(FieldIP, v))
}

// IPHasPrefix applies the HasPrefix predicate on the "ip" field.
func IPHasPrefix(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldHasPrefix(FieldIP, v))
}

// IPHasSuffix applies the HasSuffix predicate on the "ip" field.
func IPHasSuffix(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldHasSuffix(FieldIP, v))
}

// IPEqualFold applies the EqualFold predicate on the "ip" field.
func IPEqualFold(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEqualFold(FieldIP, v))
}

// IPContainsFold applies the ContainsFold predicate on the "ip" field.
func IPContainsFold(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldContainsFold(FieldIP, v))
}

// StartTimeEQ applies the EQ predicate on the "start_time" field.
func StartTimeEQ(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldStartTime, v))
}

// StartTimeNEQ applies the NEQ predicate on the "start_time" field.
func StartTimeNEQ(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldStartTime, v))
}

// StartTimeIn applies the In predicate on the "start_time" field.
func StartTimeIn(vs ...time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldStartTime, vs...))
}

// StartTimeNotIn applies the NotIn predicate on the "start_time" field.
func StartTimeNotIn(vs ...time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldStartTime, vs...))
}

// StartTimeGT applies the GT predicate on the "start_time" field.
func StartTimeGT(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGT(FieldStartTime, v))
}

// StartTimeGTE applies the GTE predicate on the "start_time" field.
func StartTimeGTE(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGTE(FieldStartTime, v))
}

// StartTimeLT applies the LT predicate on the "start_time" field.
func StartTimeLT(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLT(FieldStartTime, v))
}

// StartTimeLTE applies the LTE predicate on the "start_time" field.
func StartTimeLTE(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLTE(FieldStartTime, v))
}

// EndTimeEQ applies the EQ predicate on the "end_time" field.
func EndTimeEQ(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldEndTime, v))
}

// EndTimeNEQ applies the NEQ predicate on the "end_time" field.
func EndTimeNEQ(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldEndTime, v))
}

// EndTimeIn applies the In predicate on the "end_time" field.
func EndTimeIn(vs ...time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldEndTime, vs...))
}

// EndTimeNotIn applies the NotIn predicate on the "end_time" field.
func EndTimeNotIn(vs ...time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldEndTime, vs...))
}

// EndTimeGT applies the GT predicate on the "end_time" field.
func EndTimeGT(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGT(FieldEndTime, v))
}

// EndTimeGTE applies the GTE predicate on the "end_time" field.
func EndTimeGTE(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGTE(FieldEndTime, v))
}

// EndTimeLT applies the LT predicate on the "end_time" field.
func EndTimeLT(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLT(FieldEndTime, v))
}

// EndTimeLTE applies the LTE predicate on the "end_time" field.
func EndTimeLTE(v time.Time) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLTE(FieldEndTime, v))
}

// EndTimeIsNil applies the IsNil predicate on the "end_time" field.
func EndTimeIsNil() predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIsNull(FieldEndTime))
}

// EndTimeNotNil applies the NotNil predicate on the "end_time" field.
func EndTimeNotNil() predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotNull(FieldEndTime))
}

// DurationEQ applies the EQ predicate on the "duration" field.
func DurationEQ(v int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldDuration, v))
}

// DurationNEQ applies the NEQ predicate on the "duration" field.
func DurationNEQ(v int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldDuration, v))
}

// DurationIn applies the In predicate on the "duration" field.
func DurationIn(vs ...int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldDuration, vs...))
}

// DurationNotIn applies the NotIn predicate on the "duration" field.
func DurationNotIn(vs ...int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldDuration, vs...))
}

// DurationGT applies the GT predicate on the "duration" field.
func DurationGT(v int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGT(FieldDuration, v))
}

// DurationGTE applies the GTE predicate on the "duration" field.
func DurationGTE(v int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGTE(FieldDuration, v))
}

// DurationLT applies the LT predicate on the "duration" field.
func DurationLT(v int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLT(FieldDuration, v))
}

// DurationLTE applies the LTE predicate on the "duration" field.
func DurationLTE(v int) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLTE(FieldDuration, v))
}

// DurationIsNil applies the IsNil predicate on the "duration" field.
func DurationIsNil() predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIsNull(FieldDuration))
}

// DurationNotNil applies the NotNil predicate on the "duration" field.
func DurationNotNil() predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotNull(FieldDuration))
}

// PpsEQ applies the EQ predicate on the "pps" field.
func PpsEQ(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldPps, v))
}

// PpsNEQ applies the NEQ predicate on the "pps" field.
func PpsNEQ(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldPps, v))
}

// PpsIn applies the In predicate on the "pps" field.
func PpsIn(vs ...int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldPps, vs...))
}

// PpsNotIn applies the NotIn predicate on the "pps" field.
func PpsNotIn(vs ...int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldPps, vs...))
}

// PpsGT applies the GT predicate on the "pps" field.
func PpsGT(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGT(FieldPps, v))
}

// PpsGTE applies the GTE predicate on the "pps" field.
func PpsGTE(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGTE(FieldPps, v))
}

// PpsLT applies the LT predicate on the "pps" field.
func PpsLT(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLT(FieldPps, v))
}

// PpsLTE applies the LTE predicate on the "pps" field.
func PpsLTE(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLTE(FieldPps, v))
}

// MbpsEQ applies the EQ predicate on the "mbps" field.
func MbpsEQ(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldMbps, v))
}

// MbpsNEQ applies the NEQ predicate on the "mbps" field.
func MbpsNEQ(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldMbps, v))
}

// MbpsIn applies the In predicate on the "mbps" field.
func MbpsIn(vs ...int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldMbps, vs...))
}

// MbpsNotIn applies the NotIn predicate on the "mbps" field.
func MbpsNotIn(vs ...int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldMbps, vs...))
}

// MbpsGT applies the GT predicate on the "mbps" field.
func MbpsGT(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGT(FieldMbps, v))
}

// MbpsGTE applies the GTE predicate on the "mbps" field.
func MbpsGTE(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGTE(FieldMbps, v))
}

// MbpsLT applies the LT predicate on the "mbps" field.
func MbpsLT(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLT(FieldMbps, v))
}

// MbpsLTE applies the LTE predicate on the "mbps" field.
func MbpsLTE(v int64) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLTE(FieldMbps, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldLTE(FieldStatus, v))
}

// StatusContains applies the Contains predicate on the "status" field.
func StatusContains(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldContains(FieldStatus, v))
}

// StatusHasPrefix applies the HasPrefix predicate on the "status" field.
func StatusHasPrefix(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldHasPrefix(FieldStatus, v))
}

// StatusHasSuffix applies the HasSuffix predicate on the "status" field.
func StatusHasSuffix(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldHasSuffix(FieldStatus, v))
}

// StatusEqualFold applies the EqualFold predicate on the "status" field.
func StatusEqualFold(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldEqualFold(FieldStatus, v))
}

// StatusContainsFold applies the ContainsFold predicate on the "status" field.
func StatusContainsFold(v string) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.FieldContainsFold(FieldStatus, v))
}

// HasTenant applies the HasEdge predicate on the "tenant" edge.
func HasTenant() predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTenantWith applies the HasEdge predicate on the "tenant" edge with a given conditions (other predicates).
func HasTenantWith(preds ...predicate.Tenant) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(func(s *sql.Selector) {
		step := newTenantStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.AliCloudOriginAttackData) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.AliCloudOriginAttackData) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.AliCloudOriginAttackData) predicate.AliCloudOriginAttackData {
	return predicate.AliCloudOriginAttackData(sql.NotPredicates(p))
}
