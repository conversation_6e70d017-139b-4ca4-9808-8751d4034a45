// Code generated by ent, DO NOT EDIT.

package alicloudoriginattackdata

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the alicloudoriginattackdata type in the database.
	Label = "ali_cloud_origin_attack_data"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldTenantID holds the string denoting the tenant_id field in the database.
	FieldTenantID = "tenant_id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldRemark holds the string denoting the remark field in the database.
	FieldRemark = "remark"
	// FieldEventType holds the string denoting the event_type field in the database.
	FieldEventType = "event_type"
	// FieldProject holds the string denoting the project field in the database.
	FieldProject = "project"
	// FieldIP holds the string denoting the ip field in the database.
	FieldIP = "ip"
	// FieldRegion holds the string denoting the region field in the database.
	FieldRegion = "region"
	// FieldAction holds the string denoting the action field in the database.
	FieldAction = "action"
	// FieldTime holds the string denoting the time field in the database.
	FieldTime = "time"
	// FieldPps holds the string denoting the pps field in the database.
	FieldPps = "pps"
	// FieldBps holds the string denoting the bps field in the database.
	FieldBps = "bps"
	// FieldDdosType holds the string denoting the ddos_type field in the database.
	FieldDdosType = "ddos_type"
	// EdgeTenant holds the string denoting the tenant edge name in mutations.
	EdgeTenant = "tenant"
	// Table holds the table name of the alicloudoriginattackdata in the database.
	Table = "ali_cloud_origin_attack_data"
	// TenantTable is the table that holds the tenant relation/edge.
	TenantTable = "ali_cloud_origin_attack_data"
	// TenantInverseTable is the table name for the Tenant entity.
	// It exists in this package in order to avoid circular dependency with the "tenant" package.
	TenantInverseTable = "tenants"
	// TenantColumn is the table column denoting the tenant relation/edge.
	TenantColumn = "tenant_id"
)

// Columns holds all SQL columns for alicloudoriginattackdata fields.
var Columns = []string{
	FieldID,
	FieldTenantID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldRemark,
	FieldEventType,
	FieldProject,
	FieldIP,
	FieldRegion,
	FieldAction,
	FieldTime,
	FieldPps,
	FieldBps,
	FieldDdosType,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "meta/app/ent/runtime"
var (
	Hooks  [1]ent.Hook
	Policy ent.Policy
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	RemarkValidator func(string) error
)

// OrderOption defines the ordering options for the AliCloudOriginAttackData queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByTenantID orders the results by the tenant_id field.
func ByTenantID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTenantID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByRemark orders the results by the remark field.
func ByRemark(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRemark, opts...).ToFunc()
}

// ByEventType orders the results by the event_type field.
func ByEventType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEventType, opts...).ToFunc()
}

// ByProject orders the results by the project field.
func ByProject(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldProject, opts...).ToFunc()
}

// ByIP orders the results by the ip field.
func ByIP(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIP, opts...).ToFunc()
}

// ByRegion orders the results by the region field.
func ByRegion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRegion, opts...).ToFunc()
}

// ByAction orders the results by the action field.
func ByAction(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAction, opts...).ToFunc()
}

// ByTime orders the results by the time field.
func ByTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTime, opts...).ToFunc()
}

// ByPps orders the results by the pps field.
func ByPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPps, opts...).ToFunc()
}

// ByBps orders the results by the bps field.
func ByBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBps, opts...).ToFunc()
}

// ByDdosType orders the results by the ddos_type field.
func ByDdosType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDdosType, opts...).ToFunc()
}

// ByTenantField orders the results by tenant field.
func ByTenantField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTenantStep(), sql.OrderByField(field, opts...))
	}
}
func newTenantStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TenantInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
	)
}
