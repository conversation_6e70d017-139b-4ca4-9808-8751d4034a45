// Code generated by ent, DO NOT EDIT.

package ent

import (
	"meta/app/ent/alicloudoriginattackdata"
	"meta/app/ent/casbinrule"
	"meta/app/ent/cleandata"
	"meta/app/ent/cloudalert"
	"meta/app/ent/cloudattackdata"
	"meta/app/ent/cloudflowdata"
	"meta/app/ent/datasync"
	"meta/app/ent/group"
	"meta/app/ent/matrixspectrumalert"
	"meta/app/ent/matrixspectrumdata"
	"meta/app/ent/matrixstrategy"
	"meta/app/ent/notify"
	"meta/app/ent/predicate"
	"meta/app/ent/protectgroup"
	"meta/app/ent/skylinedos"
	"meta/app/ent/socgroupticket"
	"meta/app/ent/spectrumalert"
	"meta/app/ent/spectrumdata"
	"meta/app/ent/strategy"
	"meta/app/ent/systemapi"
	"meta/app/ent/systemconfig"
	"meta/app/ent/tenant"
	"meta/app/ent/user"
	"meta/app/ent/useroperationlog"
	"meta/app/ent/wofang"
	"meta/app/ent/wofangalert"
	"meta/app/entity"
	"strings"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqljson"
)

// QueryAliCloudOriginAttackData 查询 AliCloudOriginAttackData 值
func (acoadq *AliCloudOriginAttackDataQuery) QueryItemAliCloudOriginAttackData(input *AliCloudOriginAttackData, queryParam *entity.QueryParam, searchCount bool) *AliCloudOriginAttackDataQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			acoadq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			acoadq.Order(Asc(queryParam.Order))
		}
		acoadq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}
	var andPredicate []predicate.AliCloudOriginAttackData
	var orPredicate []predicate.AliCloudOriginAttackData

	if input.TenantID != nil {
		andPredicate = append(andPredicate, alicloudoriginattackdata.TenantID(*input.TenantID))
	}

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		acoadq.Where(alicloudoriginattackdata.And(alicloudoriginattackdata.CreatedAtGTE(queryParam.CreatedAtGte), alicloudoriginattackdata.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		acoadq.Where(alicloudoriginattackdata.And(alicloudoriginattackdata.UpdatedAtGTE(queryParam.UpdatedAtGte), alicloudoriginattackdata.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if input.Remark != nil {
		andPredicate = append(andPredicate, alicloudoriginattackdata.RemarkContainsFold(*input.Remark))
	}

	if input.EventType != "" {
		andPredicate = append(andPredicate, alicloudoriginattackdata.EventTypeContainsFold(input.EventType))
	}

	if input.Project != "" {
		andPredicate = append(andPredicate, alicloudoriginattackdata.ProjectContainsFold(input.Project))
	}

	if input.IP != "" {
		andPredicate = append(andPredicate, alicloudoriginattackdata.IPContainsFold(input.IP))
	}

	if input.Region != "" {
		andPredicate = append(andPredicate, alicloudoriginattackdata.RegionContainsFold(input.Region))
	}

	if input.Action != "" {
		andPredicate = append(andPredicate, alicloudoriginattackdata.ActionContainsFold(input.Action))
	}

	if !queryParam.TimeGte.IsZero() && !queryParam.TimeLte.IsZero() {
		acoadq.Where(alicloudoriginattackdata.And(alicloudoriginattackdata.TimeGTE(queryParam.TimeGte), alicloudoriginattackdata.TimeLTE(queryParam.TimeLte)))
	}

	if input.Pps != 0 {
		andPredicate = append(andPredicate, alicloudoriginattackdata.Pps(input.Pps))
	}

	if input.Bps != 0 {
		andPredicate = append(andPredicate, alicloudoriginattackdata.Bps(input.Bps))
	}

	if input.DdosType != "" {
		andPredicate = append(andPredicate, alicloudoriginattackdata.DdosTypeContainsFold(input.DdosType))
	}

	if len(andPredicate) != 0 {
		acoadq.Where(alicloudoriginattackdata.And(andPredicate...))
	}
	if len(orPredicate) != 0 {
		acoadq.Where(alicloudoriginattackdata.Or(orPredicate...))
	}
	return acoadq
}

// SearchAliCloudOriginAttackData 搜索 AliCloudOriginAttackData 值
func (acoadq *AliCloudOriginAttackDataQuery) SearchAliCloudOriginAttackData(input *AliCloudOriginAttackData, queryParam *entity.QueryParam, searchCount bool) *AliCloudOriginAttackDataQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			acoadq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			acoadq.Order(Asc(queryParam.Order))
		}
		acoadq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}

	var orPredicate []predicate.AliCloudOriginAttackData
	search := queryParam.Search

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		acoadq.Where(alicloudoriginattackdata.And(alicloudoriginattackdata.CreatedAtGTE(queryParam.CreatedAtGte), alicloudoriginattackdata.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		acoadq.Where(alicloudoriginattackdata.And(alicloudoriginattackdata.UpdatedAtGTE(queryParam.UpdatedAtGte), alicloudoriginattackdata.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if !queryParam.TimeGte.IsZero() && !queryParam.TimeLte.IsZero() {
		acoadq.Where(alicloudoriginattackdata.And(alicloudoriginattackdata.TimeGTE(queryParam.TimeGte), alicloudoriginattackdata.TimeLTE(queryParam.TimeLte)))
	}

	for _, v := range search {

		orPredicate = append(orPredicate, alicloudoriginattackdata.RemarkContainsFold(v))

		orPredicate = append(orPredicate, alicloudoriginattackdata.EventTypeContainsFold(v))

		orPredicate = append(orPredicate, alicloudoriginattackdata.ProjectContainsFold(v))

		orPredicate = append(orPredicate, alicloudoriginattackdata.IPContainsFold(v))

		orPredicate = append(orPredicate, alicloudoriginattackdata.RegionContainsFold(v))

		orPredicate = append(orPredicate, alicloudoriginattackdata.ActionContainsFold(v))

		orPredicate = append(orPredicate, alicloudoriginattackdata.DdosTypeContainsFold(v))

	}
	if len(orPredicate) != 0 {
		acoadq.Where(alicloudoriginattackdata.Or(orPredicate...))
	}
	return acoadq
}

// QueryCasbinRule 查询 CasbinRule 值
func (crq *CasbinRuleQuery) QueryItemCasbinRule(input *CasbinRule, queryParam *entity.QueryParam, searchCount bool) *CasbinRuleQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			crq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			crq.Order(Asc(queryParam.Order))
		}
		crq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}
	var andPredicate []predicate.CasbinRule
	var orPredicate []predicate.CasbinRule

	if input.Type != "" {
		andPredicate = append(andPredicate, casbinrule.TypeContainsFold(input.Type))
	}

	if input.Sub != "" {
		andPredicate = append(andPredicate, casbinrule.SubContainsFold(input.Sub))
	}

	if input.Dom != "" {
		andPredicate = append(andPredicate, casbinrule.DomContainsFold(input.Dom))
	}

	if input.Obj != "" {
		andPredicate = append(andPredicate, casbinrule.ObjContainsFold(input.Obj))
	}

	if input.Act != "" {
		andPredicate = append(andPredicate, casbinrule.ActContainsFold(input.Act))
	}

	if input.V4 != "" {
		andPredicate = append(andPredicate, casbinrule.V4ContainsFold(input.V4))
	}

	if input.V5 != "" {
		andPredicate = append(andPredicate, casbinrule.V5ContainsFold(input.V5))
	}

	if len(andPredicate) != 0 {
		crq.Where(casbinrule.And(andPredicate...))
	}
	if len(orPredicate) != 0 {
		crq.Where(casbinrule.Or(orPredicate...))
	}
	return crq
}

// SearchCasbinRule 搜索 CasbinRule 值
func (crq *CasbinRuleQuery) SearchCasbinRule(input *CasbinRule, queryParam *entity.QueryParam, searchCount bool) *CasbinRuleQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			crq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			crq.Order(Asc(queryParam.Order))
		}
		crq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}

	var orPredicate []predicate.CasbinRule
	search := queryParam.Search

	for _, v := range search {

		orPredicate = append(orPredicate, casbinrule.TypeContainsFold(v))

		orPredicate = append(orPredicate, casbinrule.SubContainsFold(v))

		orPredicate = append(orPredicate, casbinrule.DomContainsFold(v))

		orPredicate = append(orPredicate, casbinrule.ObjContainsFold(v))

		orPredicate = append(orPredicate, casbinrule.ActContainsFold(v))

		orPredicate = append(orPredicate, casbinrule.V4ContainsFold(v))

		orPredicate = append(orPredicate, casbinrule.V5ContainsFold(v))

	}
	if len(orPredicate) != 0 {
		crq.Where(casbinrule.Or(orPredicate...))
	}
	return crq
}

// QueryCleanData 查询 CleanData 值
func (cdq *CleanDataQuery) QueryItemCleanData(input *CleanData, queryParam *entity.QueryParam, searchCount bool) *CleanDataQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			cdq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			cdq.Order(Asc(queryParam.Order))
		}
		cdq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}
	var andPredicate []predicate.CleanData
	var orPredicate []predicate.CleanData

	if input.TenantID != nil {
		andPredicate = append(andPredicate, cleandata.TenantID(*input.TenantID))
	}

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		cdq.Where(cleandata.And(cleandata.CreatedAtGTE(queryParam.CreatedAtGte), cleandata.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if input.SpectrumAlertID != nil {
		andPredicate = append(andPredicate, cleandata.SpectrumAlertID(*input.SpectrumAlertID))
	}

	if input.IP != "" {
		andPredicate = append(andPredicate, cleandata.IPContainsFold(input.IP))
	}

	if !queryParam.TimeGte.IsZero() && !queryParam.TimeLte.IsZero() {
		cdq.Where(cleandata.And(cleandata.TimeGTE(queryParam.TimeGte), cleandata.TimeLTE(queryParam.TimeLte)))
	}

	if input.InBps != 0 {
		andPredicate = append(andPredicate, cleandata.InBps(input.InBps))
	}

	if input.OutBps != 0 {
		andPredicate = append(andPredicate, cleandata.OutBps(input.OutBps))
	}

	if input.InPps != 0 {
		andPredicate = append(andPredicate, cleandata.InPps(input.InPps))
	}

	if input.OutPps != 0 {
		andPredicate = append(andPredicate, cleandata.OutPps(input.OutPps))
	}

	if input.InAckPps != 0 {
		andPredicate = append(andPredicate, cleandata.InAckPps(input.InAckPps))
	}

	if input.OutAckPps != 0 {
		andPredicate = append(andPredicate, cleandata.OutAckPps(input.OutAckPps))
	}

	if input.InAckBps != 0 {
		andPredicate = append(andPredicate, cleandata.InAckBps(input.InAckBps))
	}

	if input.OutAckBps != 0 {
		andPredicate = append(andPredicate, cleandata.OutAckBps(input.OutAckBps))
	}

	if input.InSynPps != 0 {
		andPredicate = append(andPredicate, cleandata.InSynPps(input.InSynPps))
	}

	if input.OutSynPps != 0 {
		andPredicate = append(andPredicate, cleandata.OutSynPps(input.OutSynPps))
	}

	if input.InUDPPps != 0 {
		andPredicate = append(andPredicate, cleandata.InUDPPps(input.InUDPPps))
	}

	if input.OutUDPPps != 0 {
		andPredicate = append(andPredicate, cleandata.OutUDPPps(input.OutUDPPps))
	}

	if input.InUDPBps != 0 {
		andPredicate = append(andPredicate, cleandata.InUDPBps(input.InUDPBps))
	}

	if input.OutUDPBps != 0 {
		andPredicate = append(andPredicate, cleandata.OutUDPBps(input.OutUDPBps))
	}

	if input.InIcmpPps != 0 {
		andPredicate = append(andPredicate, cleandata.InIcmpPps(input.InIcmpPps))
	}

	if input.InIcmpBps != 0 {
		andPredicate = append(andPredicate, cleandata.InIcmpBps(input.InIcmpBps))
	}

	if input.OutIcmpBps != 0 {
		andPredicate = append(andPredicate, cleandata.OutIcmpBps(input.OutIcmpBps))
	}

	if input.OutIcmpPps != 0 {
		andPredicate = append(andPredicate, cleandata.OutIcmpPps(input.OutIcmpPps))
	}

	if input.InDNSPps != 0 {
		andPredicate = append(andPredicate, cleandata.InDNSPps(input.InDNSPps))
	}

	if input.OutDNSPps != 0 {
		andPredicate = append(andPredicate, cleandata.OutDNSPps(input.OutDNSPps))
	}

	if input.InDNSBps != 0 {
		andPredicate = append(andPredicate, cleandata.InDNSBps(input.InDNSBps))
	}

	if input.OutDNSBps != 0 {
		andPredicate = append(andPredicate, cleandata.OutDNSBps(input.OutDNSBps))
	}

	if input.CFilterID != 0 {
		andPredicate = append(andPredicate, cleandata.CFilterID(input.CFilterID))
	}

	if input.AttackFlags != 0 {
		andPredicate = append(andPredicate, cleandata.AttackFlags(input.AttackFlags))
	}

	if input.Count != 0 {
		andPredicate = append(andPredicate, cleandata.Count(input.Count))
	}

	if input.IPType != 0 {
		andPredicate = append(andPredicate, cleandata.IPType(input.IPType))
	}

	if input.CFilter != nil {
		andPredicate = append(andPredicate, cleandata.CFilterContainsFold(*input.CFilter))
	}

	if input.Host != nil {
		andPredicate = append(andPredicate, cleandata.HostContainsFold(*input.Host))
	}

	if len(andPredicate) != 0 {
		cdq.Where(cleandata.And(andPredicate...))
	}
	if len(orPredicate) != 0 {
		cdq.Where(cleandata.Or(orPredicate...))
	}
	return cdq
}

// SearchCleanData 搜索 CleanData 值
func (cdq *CleanDataQuery) SearchCleanData(input *CleanData, queryParam *entity.QueryParam, searchCount bool) *CleanDataQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			cdq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			cdq.Order(Asc(queryParam.Order))
		}
		cdq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}

	var orPredicate []predicate.CleanData
	search := queryParam.Search

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		cdq.Where(cleandata.And(cleandata.CreatedAtGTE(queryParam.CreatedAtGte), cleandata.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.TimeGte.IsZero() && !queryParam.TimeLte.IsZero() {
		cdq.Where(cleandata.And(cleandata.TimeGTE(queryParam.TimeGte), cleandata.TimeLTE(queryParam.TimeLte)))
	}

	for _, v := range search {

		orPredicate = append(orPredicate, cleandata.IPContainsFold(v))

		orPredicate = append(orPredicate, cleandata.CFilterContainsFold(v))

		orPredicate = append(orPredicate, cleandata.HostContainsFold(v))

	}
	if len(orPredicate) != 0 {
		cdq.Where(cleandata.Or(orPredicate...))
	}
	return cdq
}

// QueryCloudAlert 查询 CloudAlert 值
func (caq *CloudAlertQuery) QueryItemCloudAlert(input *CloudAlert, queryParam *entity.QueryParam, searchCount bool) *CloudAlertQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			caq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			caq.Order(Asc(queryParam.Order))
		}
		caq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}
	var andPredicate []predicate.CloudAlert
	var orPredicate []predicate.CloudAlert

	if input.TenantID != nil {
		andPredicate = append(andPredicate, cloudalert.TenantID(*input.TenantID))
	}

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		caq.Where(cloudalert.And(cloudalert.CreatedAtGTE(queryParam.CreatedAtGte), cloudalert.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		caq.Where(cloudalert.And(cloudalert.UpdatedAtGTE(queryParam.UpdatedAtGte), cloudalert.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if input.Remark != nil {
		andPredicate = append(andPredicate, cloudalert.RemarkContainsFold(*input.Remark))
	}

	if input.SrcIP != "" {
		andPredicate = append(andPredicate, cloudalert.SrcIPContainsFold(input.SrcIP))
	}

	if input.SrcPort != 0 {
		andPredicate = append(andPredicate, cloudalert.SrcPort(input.SrcPort))
	}

	if input.DstIP != "" {
		andPredicate = append(andPredicate, cloudalert.DstIPContainsFold(input.DstIP))
	}

	if input.DstPort != 0 {
		andPredicate = append(andPredicate, cloudalert.DstPort(input.DstPort))
	}

	if input.DefenceMode != 0 {
		andPredicate = append(andPredicate, cloudalert.DefenceMode(input.DefenceMode))
	}

	if input.FlowMode != 0 {
		andPredicate = append(andPredicate, cloudalert.FlowMode(input.FlowMode))
	}

	if input.TCPAckNum != "" {
		andPredicate = append(andPredicate, cloudalert.TCPAckNumContainsFold(input.TCPAckNum))
	}

	if input.TCPSeqNum != "" {
		andPredicate = append(andPredicate, cloudalert.TCPSeqNumContainsFold(input.TCPSeqNum))
	}

	if input.Protocol != 0 {
		andPredicate = append(andPredicate, cloudalert.Protocol(input.Protocol))
	}

	if input.DefenceLevel != 0 {
		andPredicate = append(andPredicate, cloudalert.DefenceLevel(input.DefenceLevel))
	}

	if input.MaxPps != 0 {
		andPredicate = append(andPredicate, cloudalert.MaxPps(input.MaxPps))
	}

	if input.MaxAttackPps != 0 {
		andPredicate = append(andPredicate, cloudalert.MaxAttackPps(input.MaxAttackPps))
	}

	if input.OverlimitPktCount != 0 {
		andPredicate = append(andPredicate, cloudalert.OverlimitPktCount(input.OverlimitPktCount))
	}

	if !queryParam.StartTimeGte.IsZero() && !queryParam.StartTimeLte.IsZero() {
		caq.Where(cloudalert.And(cloudalert.StartTimeGTE(queryParam.StartTimeGte), cloudalert.StartTimeLTE(queryParam.StartTimeLte)))
	}

	if !queryParam.EndTimeGte.IsZero() && !queryParam.EndTimeLte.IsZero() {
		caq.Where(cloudalert.And(cloudalert.EndTimeGTE(queryParam.EndTimeGte), cloudalert.EndTimeLTE(queryParam.EndTimeLte)))
	}

	if len(andPredicate) != 0 {
		caq.Where(cloudalert.And(andPredicate...))
	}
	if len(orPredicate) != 0 {
		caq.Where(cloudalert.Or(orPredicate...))
	}
	return caq
}

// SearchCloudAlert 搜索 CloudAlert 值
func (caq *CloudAlertQuery) SearchCloudAlert(input *CloudAlert, queryParam *entity.QueryParam, searchCount bool) *CloudAlertQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			caq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			caq.Order(Asc(queryParam.Order))
		}
		caq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}

	var orPredicate []predicate.CloudAlert
	search := queryParam.Search

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		caq.Where(cloudalert.And(cloudalert.CreatedAtGTE(queryParam.CreatedAtGte), cloudalert.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		caq.Where(cloudalert.And(cloudalert.UpdatedAtGTE(queryParam.UpdatedAtGte), cloudalert.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if !queryParam.StartTimeGte.IsZero() && !queryParam.StartTimeLte.IsZero() {
		caq.Where(cloudalert.And(cloudalert.StartTimeGTE(queryParam.StartTimeGte), cloudalert.StartTimeLTE(queryParam.StartTimeLte)))
	}

	if !queryParam.EndTimeGte.IsZero() && !queryParam.EndTimeLte.IsZero() {
		caq.Where(cloudalert.And(cloudalert.EndTimeGTE(queryParam.EndTimeGte), cloudalert.EndTimeLTE(queryParam.EndTimeLte)))
	}

	for _, v := range search {

		orPredicate = append(orPredicate, cloudalert.RemarkContainsFold(v))

		orPredicate = append(orPredicate, cloudalert.SrcIPContainsFold(v))

		orPredicate = append(orPredicate, cloudalert.DstIPContainsFold(v))

		orPredicate = append(orPredicate, cloudalert.TCPAckNumContainsFold(v))

		orPredicate = append(orPredicate, cloudalert.TCPSeqNumContainsFold(v))

	}
	if len(orPredicate) != 0 {
		caq.Where(cloudalert.Or(orPredicate...))
	}
	return caq
}

// QueryCloudAttackData 查询 CloudAttackData 值
func (cadq *CloudAttackDataQuery) QueryItemCloudAttackData(input *CloudAttackData, queryParam *entity.QueryParam, searchCount bool) *CloudAttackDataQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			cadq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			cadq.Order(Asc(queryParam.Order))
		}
		cadq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}
	var andPredicate []predicate.CloudAttackData
	var orPredicate []predicate.CloudAttackData

	if input.TenantID != nil {
		andPredicate = append(andPredicate, cloudattackdata.TenantID(*input.TenantID))
	}

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		cadq.Where(cloudattackdata.And(cloudattackdata.CreatedAtGTE(queryParam.CreatedAtGte), cloudattackdata.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		cadq.Where(cloudattackdata.And(cloudattackdata.UpdatedAtGTE(queryParam.UpdatedAtGte), cloudattackdata.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if input.Remark != nil {
		andPredicate = append(andPredicate, cloudattackdata.RemarkContainsFold(*input.Remark))
	}

	if input.SrcIP != "" {
		andPredicate = append(andPredicate, cloudattackdata.SrcIPContainsFold(input.SrcIP))
	}

	if input.SrcPort != 0 {
		andPredicate = append(andPredicate, cloudattackdata.SrcPort(input.SrcPort))
	}

	if input.DstIP != "" {
		andPredicate = append(andPredicate, cloudattackdata.DstIPContainsFold(input.DstIP))
	}

	if input.DstPort != 0 {
		andPredicate = append(andPredicate, cloudattackdata.DstPort(input.DstPort))
	}

	if input.Protocol != 0 {
		andPredicate = append(andPredicate, cloudattackdata.Protocol(input.Protocol))
	}

	if input.CurrentAttackPps != 0 {
		andPredicate = append(andPredicate, cloudattackdata.CurrentAttackPps(input.CurrentAttackPps))
	}

	if !queryParam.StartTimeGte.IsZero() && !queryParam.StartTimeLte.IsZero() {
		cadq.Where(cloudattackdata.And(cloudattackdata.StartTimeGTE(queryParam.StartTimeGte), cloudattackdata.StartTimeLTE(queryParam.StartTimeLte)))
	}

	if !queryParam.EndTimeGte.IsZero() && !queryParam.EndTimeLte.IsZero() {
		cadq.Where(cloudattackdata.And(cloudattackdata.EndTimeGTE(queryParam.EndTimeGte), cloudattackdata.EndTimeLTE(queryParam.EndTimeLte)))
	}

	if len(andPredicate) != 0 {
		cadq.Where(cloudattackdata.And(andPredicate...))
	}
	if len(orPredicate) != 0 {
		cadq.Where(cloudattackdata.Or(orPredicate...))
	}
	return cadq
}

// SearchCloudAttackData 搜索 CloudAttackData 值
func (cadq *CloudAttackDataQuery) SearchCloudAttackData(input *CloudAttackData, queryParam *entity.QueryParam, searchCount bool) *CloudAttackDataQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			cadq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			cadq.Order(Asc(queryParam.Order))
		}
		cadq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}

	var orPredicate []predicate.CloudAttackData
	search := queryParam.Search

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		cadq.Where(cloudattackdata.And(cloudattackdata.CreatedAtGTE(queryParam.CreatedAtGte), cloudattackdata.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		cadq.Where(cloudattackdata.And(cloudattackdata.UpdatedAtGTE(queryParam.UpdatedAtGte), cloudattackdata.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if !queryParam.StartTimeGte.IsZero() && !queryParam.StartTimeLte.IsZero() {
		cadq.Where(cloudattackdata.And(cloudattackdata.StartTimeGTE(queryParam.StartTimeGte), cloudattackdata.StartTimeLTE(queryParam.StartTimeLte)))
	}

	if !queryParam.EndTimeGte.IsZero() && !queryParam.EndTimeLte.IsZero() {
		cadq.Where(cloudattackdata.And(cloudattackdata.EndTimeGTE(queryParam.EndTimeGte), cloudattackdata.EndTimeLTE(queryParam.EndTimeLte)))
	}

	for _, v := range search {

		orPredicate = append(orPredicate, cloudattackdata.RemarkContainsFold(v))

		orPredicate = append(orPredicate, cloudattackdata.SrcIPContainsFold(v))

		orPredicate = append(orPredicate, cloudattackdata.DstIPContainsFold(v))

	}
	if len(orPredicate) != 0 {
		cadq.Where(cloudattackdata.Or(orPredicate...))
	}
	return cadq
}

// QueryCloudFlowData 查询 CloudFlowData 值
func (cfdq *CloudFlowDataQuery) QueryItemCloudFlowData(input *CloudFlowData, queryParam *entity.QueryParam, searchCount bool) *CloudFlowDataQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			cfdq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			cfdq.Order(Asc(queryParam.Order))
		}
		cfdq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}
	var andPredicate []predicate.CloudFlowData
	var orPredicate []predicate.CloudFlowData

	if input.TenantID != nil {
		andPredicate = append(andPredicate, cloudflowdata.TenantID(*input.TenantID))
	}

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		cfdq.Where(cloudflowdata.And(cloudflowdata.CreatedAtGTE(queryParam.CreatedAtGte), cloudflowdata.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		cfdq.Where(cloudflowdata.And(cloudflowdata.UpdatedAtGTE(queryParam.UpdatedAtGte), cloudflowdata.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if input.Remark != nil {
		andPredicate = append(andPredicate, cloudflowdata.RemarkContainsFold(*input.Remark))
	}

	if input.CloudAlertID != nil {
		andPredicate = append(andPredicate, cloudflowdata.CloudAlertID(*input.CloudAlertID))
	}

	if input.SrcIP != "" {
		andPredicate = append(andPredicate, cloudflowdata.SrcIPContainsFold(input.SrcIP))
	}

	if input.SrcPort != 0 {
		andPredicate = append(andPredicate, cloudflowdata.SrcPort(input.SrcPort))
	}

	if input.DstIP != "" {
		andPredicate = append(andPredicate, cloudflowdata.DstIPContainsFold(input.DstIP))
	}

	if input.DstPort != 0 {
		andPredicate = append(andPredicate, cloudflowdata.DstPort(input.DstPort))
	}

	if input.Protocol != 0 {
		andPredicate = append(andPredicate, cloudflowdata.Protocol(input.Protocol))
	}

	if input.MaxAttackPps != 0 {
		andPredicate = append(andPredicate, cloudflowdata.MaxAttackPps(input.MaxAttackPps))
	}

	if input.FlowOverMaxPpsCount != 0 {
		andPredicate = append(andPredicate, cloudflowdata.FlowOverMaxPpsCount(input.FlowOverMaxPpsCount))
	}

	if !queryParam.StartTimeGte.IsZero() && !queryParam.StartTimeLte.IsZero() {
		cfdq.Where(cloudflowdata.And(cloudflowdata.StartTimeGTE(queryParam.StartTimeGte), cloudflowdata.StartTimeLTE(queryParam.StartTimeLte)))
	}

	if !queryParam.EndTimeGte.IsZero() && !queryParam.EndTimeLte.IsZero() {
		cfdq.Where(cloudflowdata.And(cloudflowdata.EndTimeGTE(queryParam.EndTimeGte), cloudflowdata.EndTimeLTE(queryParam.EndTimeLte)))
	}

	if len(andPredicate) != 0 {
		cfdq.Where(cloudflowdata.And(andPredicate...))
	}
	if len(orPredicate) != 0 {
		cfdq.Where(cloudflowdata.Or(orPredicate...))
	}
	return cfdq
}

// SearchCloudFlowData 搜索 CloudFlowData 值
func (cfdq *CloudFlowDataQuery) SearchCloudFlowData(input *CloudFlowData, queryParam *entity.QueryParam, searchCount bool) *CloudFlowDataQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			cfdq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			cfdq.Order(Asc(queryParam.Order))
		}
		cfdq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}

	var orPredicate []predicate.CloudFlowData
	search := queryParam.Search

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		cfdq.Where(cloudflowdata.And(cloudflowdata.CreatedAtGTE(queryParam.CreatedAtGte), cloudflowdata.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		cfdq.Where(cloudflowdata.And(cloudflowdata.UpdatedAtGTE(queryParam.UpdatedAtGte), cloudflowdata.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if !queryParam.StartTimeGte.IsZero() && !queryParam.StartTimeLte.IsZero() {
		cfdq.Where(cloudflowdata.And(cloudflowdata.StartTimeGTE(queryParam.StartTimeGte), cloudflowdata.StartTimeLTE(queryParam.StartTimeLte)))
	}

	if !queryParam.EndTimeGte.IsZero() && !queryParam.EndTimeLte.IsZero() {
		cfdq.Where(cloudflowdata.And(cloudflowdata.EndTimeGTE(queryParam.EndTimeGte), cloudflowdata.EndTimeLTE(queryParam.EndTimeLte)))
	}

	for _, v := range search {

		orPredicate = append(orPredicate, cloudflowdata.RemarkContainsFold(v))

		orPredicate = append(orPredicate, cloudflowdata.SrcIPContainsFold(v))

		orPredicate = append(orPredicate, cloudflowdata.DstIPContainsFold(v))

	}
	if len(orPredicate) != 0 {
		cfdq.Where(cloudflowdata.Or(orPredicate...))
	}
	return cfdq
}

// QueryDataSync 查询 DataSync 值
func (dsq *DataSyncQuery) QueryItemDataSync(input *DataSync, queryParam *entity.QueryParam, searchCount bool) *DataSyncQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			dsq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			dsq.Order(Asc(queryParam.Order))
		}
		dsq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}
	var andPredicate []predicate.DataSync
	var orPredicate []predicate.DataSync

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		dsq.Where(datasync.And(datasync.CreatedAtGTE(queryParam.CreatedAtGte), datasync.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		dsq.Where(datasync.And(datasync.UpdatedAtGTE(queryParam.UpdatedAtGte), datasync.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if input.Remark != nil {
		andPredicate = append(andPredicate, datasync.RemarkContainsFold(*input.Remark))
	}

	if input.PreDataList != nil {
		for _, i := range *input.PreDataList {
			j := i
			orPredicate = append(orPredicate,
				func(s *sql.Selector) {
					s.Where(sqljson.StringContains(datasync.FieldPreDataList, j))
				})
		}
	}

	if input.DataList != nil {
		for _, i := range *input.DataList {
			j := i
			orPredicate = append(orPredicate,
				func(s *sql.Selector) {
					s.Where(sqljson.StringContains(datasync.FieldDataList, j))
				})
		}
	}

	if input.DataType != "" {
		andPredicate = append(andPredicate, datasync.DataTypeContainsFold(input.DataType))
	}

	if input.Type != "" {
		andPredicate = append(andPredicate, datasync.TypeContainsFold(input.Type))
	}

	if len(andPredicate) != 0 {
		dsq.Where(datasync.And(andPredicate...))
	}
	if len(orPredicate) != 0 {
		dsq.Where(datasync.Or(orPredicate...))
	}
	return dsq
}

// SearchDataSync 搜索 DataSync 值
func (dsq *DataSyncQuery) SearchDataSync(input *DataSync, queryParam *entity.QueryParam, searchCount bool) *DataSyncQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			dsq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			dsq.Order(Asc(queryParam.Order))
		}
		dsq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}

	var orPredicate []predicate.DataSync
	search := queryParam.Search

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		dsq.Where(datasync.And(datasync.CreatedAtGTE(queryParam.CreatedAtGte), datasync.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		dsq.Where(datasync.And(datasync.UpdatedAtGTE(queryParam.UpdatedAtGte), datasync.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	for _, v := range search {

		orPredicate = append(orPredicate, datasync.RemarkContainsFold(v))

		orPredicate = append(orPredicate, datasync.DataTypeContainsFold(v))

		orPredicate = append(orPredicate, datasync.TypeContainsFold(v))

	}
	if len(orPredicate) != 0 {
		dsq.Where(datasync.Or(orPredicate...))
	}
	return dsq
}

// QueryGroup 查询 Group 值
func (gq *GroupQuery) QueryItemGroup(input *Group, queryParam *entity.QueryParam, searchCount bool) *GroupQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			gq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			gq.Order(Asc(queryParam.Order))
		}
		gq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}
	var andPredicate []predicate.Group
	var orPredicate []predicate.Group

	if input.TenantID != nil {
		andPredicate = append(andPredicate, group.TenantID(*input.TenantID))
	}

	if input.Name != "" {
		andPredicate = append(andPredicate, group.NameContainsFold(input.Name))
	}

	if len(andPredicate) != 0 {
		gq.Where(group.And(andPredicate...))
	}
	if len(orPredicate) != 0 {
		gq.Where(group.Or(orPredicate...))
	}
	return gq
}

// SearchGroup 搜索 Group 值
func (gq *GroupQuery) SearchGroup(input *Group, queryParam *entity.QueryParam, searchCount bool) *GroupQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			gq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			gq.Order(Asc(queryParam.Order))
		}
		gq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}

	var orPredicate []predicate.Group
	search := queryParam.Search

	for _, v := range search {

		orPredicate = append(orPredicate, group.NameContainsFold(v))

	}
	if len(orPredicate) != 0 {
		gq.Where(group.Or(orPredicate...))
	}
	return gq
}

// QueryMatrixSpectrumAlert 查询 MatrixSpectrumAlert 值
func (msaq *MatrixSpectrumAlertQuery) QueryItemMatrixSpectrumAlert(input *MatrixSpectrumAlert, queryParam *entity.QueryParam, searchCount bool) *MatrixSpectrumAlertQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			msaq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			msaq.Order(Asc(queryParam.Order))
		}
		msaq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}
	var andPredicate []predicate.MatrixSpectrumAlert
	var orPredicate []predicate.MatrixSpectrumAlert

	if input.TenantID != nil {
		andPredicate = append(andPredicate, matrixspectrumalert.TenantID(*input.TenantID))
	}

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		msaq.Where(matrixspectrumalert.And(matrixspectrumalert.CreatedAtGTE(queryParam.CreatedAtGte), matrixspectrumalert.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		msaq.Where(matrixspectrumalert.And(matrixspectrumalert.UpdatedAtGTE(queryParam.UpdatedAtGte), matrixspectrumalert.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if input.Remark != nil {
		andPredicate = append(andPredicate, matrixspectrumalert.RemarkContainsFold(*input.Remark))
	}

	if input.WofangID != nil {
		andPredicate = append(andPredicate, matrixspectrumalert.WofangID(*input.WofangID))
	}

	if input.MatrixStrategyID != nil {
		andPredicate = append(andPredicate, matrixspectrumalert.MatrixStrategyID(*input.MatrixStrategyID))
	}

	if input.IPList != nil {
		for _, i := range *input.IPList {
			j := i
			orPredicate = append(orPredicate,
				func(s *sql.Selector) {
					s.Where(sqljson.StringContains(matrixspectrumalert.FieldIPList, j))
				})
		}
	}

	if input.Region != "" {
		andPredicate = append(andPredicate, matrixspectrumalert.RegionContainsFold(input.Region))
	}

	if input.NetType != "" {
		andPredicate = append(andPredicate, matrixspectrumalert.NetTypeContainsFold(input.NetType))
	}

	if input.Isp != "" {
		andPredicate = append(andPredicate, matrixspectrumalert.IspContainsFold(input.Isp))
	}

	if !queryParam.StartTimeGte.IsZero() && !queryParam.StartTimeLte.IsZero() {
		msaq.Where(matrixspectrumalert.And(matrixspectrumalert.StartTimeGTE(queryParam.StartTimeGte), matrixspectrumalert.StartTimeLTE(queryParam.StartTimeLte)))
	}

	if !queryParam.EndTimeGte.IsZero() && !queryParam.EndTimeLte.IsZero() {
		msaq.Where(matrixspectrumalert.And(matrixspectrumalert.EndTimeGTE(queryParam.EndTimeGte), matrixspectrumalert.EndTimeLTE(queryParam.EndTimeLte)))
	}

	if input.AttackType != "" {
		andPredicate = append(andPredicate, matrixspectrumalert.AttackTypeContainsFold(input.AttackType))
	}

	if input.Bps != 0 {
		andPredicate = append(andPredicate, matrixspectrumalert.Bps(input.Bps))
	}

	if len(andPredicate) != 0 {
		msaq.Where(matrixspectrumalert.And(andPredicate...))
	}
	if len(orPredicate) != 0 {
		msaq.Where(matrixspectrumalert.Or(orPredicate...))
	}
	return msaq
}

// SearchMatrixSpectrumAlert 搜索 MatrixSpectrumAlert 值
func (msaq *MatrixSpectrumAlertQuery) SearchMatrixSpectrumAlert(input *MatrixSpectrumAlert, queryParam *entity.QueryParam, searchCount bool) *MatrixSpectrumAlertQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			msaq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			msaq.Order(Asc(queryParam.Order))
		}
		msaq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}

	var orPredicate []predicate.MatrixSpectrumAlert
	search := queryParam.Search

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		msaq.Where(matrixspectrumalert.And(matrixspectrumalert.CreatedAtGTE(queryParam.CreatedAtGte), matrixspectrumalert.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		msaq.Where(matrixspectrumalert.And(matrixspectrumalert.UpdatedAtGTE(queryParam.UpdatedAtGte), matrixspectrumalert.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if !queryParam.StartTimeGte.IsZero() && !queryParam.StartTimeLte.IsZero() {
		msaq.Where(matrixspectrumalert.And(matrixspectrumalert.StartTimeGTE(queryParam.StartTimeGte), matrixspectrumalert.StartTimeLTE(queryParam.StartTimeLte)))
	}

	if !queryParam.EndTimeGte.IsZero() && !queryParam.EndTimeLte.IsZero() {
		msaq.Where(matrixspectrumalert.And(matrixspectrumalert.EndTimeGTE(queryParam.EndTimeGte), matrixspectrumalert.EndTimeLTE(queryParam.EndTimeLte)))
	}

	for _, v := range search {

		orPredicate = append(orPredicate, matrixspectrumalert.RemarkContainsFold(v))

		orPredicate = append(orPredicate, matrixspectrumalert.RegionContainsFold(v))

		orPredicate = append(orPredicate, matrixspectrumalert.NetTypeContainsFold(v))

		orPredicate = append(orPredicate, matrixspectrumalert.IspContainsFold(v))

		orPredicate = append(orPredicate, matrixspectrumalert.AttackTypeContainsFold(v))

	}
	if len(orPredicate) != 0 {
		msaq.Where(matrixspectrumalert.Or(orPredicate...))
	}
	return msaq
}

// QueryMatrixSpectrumData 查询 MatrixSpectrumData 值
func (msdq *MatrixSpectrumDataQuery) QueryItemMatrixSpectrumData(input *MatrixSpectrumData, queryParam *entity.QueryParam, searchCount bool) *MatrixSpectrumDataQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			msdq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			msdq.Order(Asc(queryParam.Order))
		}
		msdq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}
	var andPredicate []predicate.MatrixSpectrumData
	var orPredicate []predicate.MatrixSpectrumData

	if input.TenantID != nil {
		andPredicate = append(andPredicate, matrixspectrumdata.TenantID(*input.TenantID))
	}

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		msdq.Where(matrixspectrumdata.And(matrixspectrumdata.CreatedAtGTE(queryParam.CreatedAtGte), matrixspectrumdata.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		msdq.Where(matrixspectrumdata.And(matrixspectrumdata.UpdatedAtGTE(queryParam.UpdatedAtGte), matrixspectrumdata.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if input.MatrixSpectrumAlertID != nil {
		andPredicate = append(andPredicate, matrixspectrumdata.MatrixSpectrumAlertID(*input.MatrixSpectrumAlertID))
	}

	if input.Region != "" {
		andPredicate = append(andPredicate, matrixspectrumdata.RegionContainsFold(input.Region))
	}

	if input.NetType != "" {
		andPredicate = append(andPredicate, matrixspectrumdata.NetTypeContainsFold(input.NetType))
	}

	if input.Isp != "" {
		andPredicate = append(andPredicate, matrixspectrumdata.IspContainsFold(input.Isp))
	}

	if input.Bps != 0 {
		andPredicate = append(andPredicate, matrixspectrumdata.Bps(input.Bps))
	}

	if !queryParam.TimeGte.IsZero() && !queryParam.TimeLte.IsZero() {
		msdq.Where(matrixspectrumdata.And(matrixspectrumdata.TimeGTE(queryParam.TimeGte), matrixspectrumdata.TimeLTE(queryParam.TimeLte)))
	}

	if len(andPredicate) != 0 {
		msdq.Where(matrixspectrumdata.And(andPredicate...))
	}
	if len(orPredicate) != 0 {
		msdq.Where(matrixspectrumdata.Or(orPredicate...))
	}
	return msdq
}

// SearchMatrixSpectrumData 搜索 MatrixSpectrumData 值
func (msdq *MatrixSpectrumDataQuery) SearchMatrixSpectrumData(input *MatrixSpectrumData, queryParam *entity.QueryParam, searchCount bool) *MatrixSpectrumDataQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			msdq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			msdq.Order(Asc(queryParam.Order))
		}
		msdq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}

	var orPredicate []predicate.MatrixSpectrumData
	search := queryParam.Search

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		msdq.Where(matrixspectrumdata.And(matrixspectrumdata.CreatedAtGTE(queryParam.CreatedAtGte), matrixspectrumdata.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		msdq.Where(matrixspectrumdata.And(matrixspectrumdata.UpdatedAtGTE(queryParam.UpdatedAtGte), matrixspectrumdata.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if !queryParam.TimeGte.IsZero() && !queryParam.TimeLte.IsZero() {
		msdq.Where(matrixspectrumdata.And(matrixspectrumdata.TimeGTE(queryParam.TimeGte), matrixspectrumdata.TimeLTE(queryParam.TimeLte)))
	}

	for _, v := range search {

		orPredicate = append(orPredicate, matrixspectrumdata.RegionContainsFold(v))

		orPredicate = append(orPredicate, matrixspectrumdata.NetTypeContainsFold(v))

		orPredicate = append(orPredicate, matrixspectrumdata.IspContainsFold(v))

	}
	if len(orPredicate) != 0 {
		msdq.Where(matrixspectrumdata.Or(orPredicate...))
	}
	return msdq
}

// QueryMatrixStrategy 查询 MatrixStrategy 值
func (msq *MatrixStrategyQuery) QueryItemMatrixStrategy(input *MatrixStrategy, queryParam *entity.QueryParam, searchCount bool) *MatrixStrategyQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			msq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			msq.Order(Asc(queryParam.Order))
		}
		msq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}
	var andPredicate []predicate.MatrixStrategy
	var orPredicate []predicate.MatrixStrategy

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		msq.Where(matrixstrategy.And(matrixstrategy.CreatedAtGTE(queryParam.CreatedAtGte), matrixstrategy.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		msq.Where(matrixstrategy.And(matrixstrategy.UpdatedAtGTE(queryParam.UpdatedAtGte), matrixstrategy.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if input.Remark != nil {
		andPredicate = append(andPredicate, matrixstrategy.RemarkContainsFold(*input.Remark))
	}

	if input.Name != "" {
		andPredicate = append(andPredicate, matrixstrategy.NameContainsFold(input.Name))
	}

	if input.Region != "" {
		andPredicate = append(andPredicate, matrixstrategy.RegionContainsFold(input.Region))
	}

	if input.NetType != "" {
		andPredicate = append(andPredicate, matrixstrategy.NetTypeContainsFold(input.NetType))
	}

	if input.Isp != "" {
		andPredicate = append(andPredicate, matrixstrategy.IspContainsFold(input.Isp))
	}

	if input.MonitorBps != 0 {
		andPredicate = append(andPredicate, matrixstrategy.MonitorBps(input.MonitorBps))
	}

	if input.DragBps != 0 {
		andPredicate = append(andPredicate, matrixstrategy.DragBps(input.DragBps))
	}

	if input.DragType != 0 {
		andPredicate = append(andPredicate, matrixstrategy.DragType(input.DragType))
	}

	if len(andPredicate) != 0 {
		msq.Where(matrixstrategy.And(andPredicate...))
	}
	if len(orPredicate) != 0 {
		msq.Where(matrixstrategy.Or(orPredicate...))
	}
	return msq
}

// SearchMatrixStrategy 搜索 MatrixStrategy 值
func (msq *MatrixStrategyQuery) SearchMatrixStrategy(input *MatrixStrategy, queryParam *entity.QueryParam, searchCount bool) *MatrixStrategyQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			msq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			msq.Order(Asc(queryParam.Order))
		}
		msq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}

	var orPredicate []predicate.MatrixStrategy
	search := queryParam.Search

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		msq.Where(matrixstrategy.And(matrixstrategy.CreatedAtGTE(queryParam.CreatedAtGte), matrixstrategy.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		msq.Where(matrixstrategy.And(matrixstrategy.UpdatedAtGTE(queryParam.UpdatedAtGte), matrixstrategy.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	for _, v := range search {

		orPredicate = append(orPredicate, matrixstrategy.RemarkContainsFold(v))

		orPredicate = append(orPredicate, matrixstrategy.NameContainsFold(v))

		orPredicate = append(orPredicate, matrixstrategy.RegionContainsFold(v))

		orPredicate = append(orPredicate, matrixstrategy.NetTypeContainsFold(v))

		orPredicate = append(orPredicate, matrixstrategy.IspContainsFold(v))

	}
	if len(orPredicate) != 0 {
		msq.Where(matrixstrategy.Or(orPredicate...))
	}
	return msq
}

// QueryNotify 查询 Notify 值
func (nq *NotifyQuery) QueryItemNotify(input *Notify, queryParam *entity.QueryParam, searchCount bool) *NotifyQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			nq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			nq.Order(Asc(queryParam.Order))
		}
		nq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}
	var andPredicate []predicate.Notify
	var orPredicate []predicate.Notify

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		nq.Where(notify.And(notify.CreatedAtGTE(queryParam.CreatedAtGte), notify.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		nq.Where(notify.And(notify.UpdatedAtGTE(queryParam.UpdatedAtGte), notify.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if input.TenantID != nil {
		andPredicate = append(andPredicate, notify.TenantID(*input.TenantID))
	}

	if input.Remark != nil {
		andPredicate = append(andPredicate, notify.RemarkContainsFold(*input.Remark))
	}

	if input.Name != "" {
		andPredicate = append(andPredicate, notify.NameContainsFold(input.Name))
	}

	if input.PopoGroups != nil {
		for _, i := range *input.PopoGroups {
			j := i
			orPredicate = append(orPredicate,
				func(s *sql.Selector) {
					s.Where(sqljson.StringContains(notify.FieldPopoGroups, j))
				})
		}
	}

	if input.Emails != nil {
		for _, i := range *input.Emails {
			j := i
			orPredicate = append(orPredicate,
				func(s *sql.Selector) {
					s.Where(sqljson.StringContains(notify.FieldEmails, j))
				})
		}
	}

	if input.Phones != nil {
		for _, i := range *input.Phones {
			j := i
			orPredicate = append(orPredicate,
				func(s *sql.Selector) {
					s.Where(sqljson.StringContains(notify.FieldPhones, j))
				})
		}
	}

	if input.IPWhitelists != nil {
		for _, i := range *input.IPWhitelists {
			j := i
			orPredicate = append(orPredicate,
				func(s *sql.Selector) {
					s.Where(sqljson.StringContains(notify.FieldIPWhitelists, j))
				})
		}
	}

	if len(andPredicate) != 0 {
		nq.Where(notify.And(andPredicate...))
	}
	if len(orPredicate) != 0 {
		nq.Where(notify.Or(orPredicate...))
	}
	return nq
}

// SearchNotify 搜索 Notify 值
func (nq *NotifyQuery) SearchNotify(input *Notify, queryParam *entity.QueryParam, searchCount bool) *NotifyQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			nq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			nq.Order(Asc(queryParam.Order))
		}
		nq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}

	var orPredicate []predicate.Notify
	search := queryParam.Search

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		nq.Where(notify.And(notify.CreatedAtGTE(queryParam.CreatedAtGte), notify.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		nq.Where(notify.And(notify.UpdatedAtGTE(queryParam.UpdatedAtGte), notify.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	for _, v := range search {

		orPredicate = append(orPredicate, notify.RemarkContainsFold(v))

		orPredicate = append(orPredicate, notify.NameContainsFold(v))

	}
	if len(orPredicate) != 0 {
		nq.Where(notify.Or(orPredicate...))
	}
	return nq
}

// QueryProtectGroup 查询 ProtectGroup 值
func (pgq *ProtectGroupQuery) QueryItemProtectGroup(input *ProtectGroup, queryParam *entity.QueryParam, searchCount bool) *ProtectGroupQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			pgq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			pgq.Order(Asc(queryParam.Order))
		}
		pgq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}
	var andPredicate []predicate.ProtectGroup
	var orPredicate []predicate.ProtectGroup

	if input.TenantID != nil {
		andPredicate = append(andPredicate, protectgroup.TenantID(*input.TenantID))
	}

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		pgq.Where(protectgroup.And(protectgroup.CreatedAtGTE(queryParam.CreatedAtGte), protectgroup.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		pgq.Where(protectgroup.And(protectgroup.UpdatedAtGTE(queryParam.UpdatedAtGte), protectgroup.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if input.Remark != nil {
		andPredicate = append(andPredicate, protectgroup.RemarkContainsFold(*input.Remark))
	}

	if input.GroupName != "" {
		andPredicate = append(andPredicate, protectgroup.GroupNameContainsFold(input.GroupName))
	}

	if input.GroupID != 0 {
		andPredicate = append(andPredicate, protectgroup.GroupID(input.GroupID))
	}

	if input.Type != 0 {
		andPredicate = append(andPredicate, protectgroup.Type(input.Type))
	}

	if input.IPList != nil {
		for _, i := range *input.IPList {
			j := i
			orPredicate = append(orPredicate,
				func(s *sql.Selector) {
					s.Where(sqljson.StringContains(protectgroup.FieldIPList, j))
				})
		}
	}

	if input.ExpandIP != "" {
		andPredicate = append(andPredicate, protectgroup.ExpandIPContainsFold(input.ExpandIP))
	}

	if len(andPredicate) != 0 {
		pgq.Where(protectgroup.And(andPredicate...))
	}
	if len(orPredicate) != 0 {
		pgq.Where(protectgroup.Or(orPredicate...))
	}
	return pgq
}

// SearchProtectGroup 搜索 ProtectGroup 值
func (pgq *ProtectGroupQuery) SearchProtectGroup(input *ProtectGroup, queryParam *entity.QueryParam, searchCount bool) *ProtectGroupQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			pgq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			pgq.Order(Asc(queryParam.Order))
		}
		pgq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}

	var orPredicate []predicate.ProtectGroup
	search := queryParam.Search

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		pgq.Where(protectgroup.And(protectgroup.CreatedAtGTE(queryParam.CreatedAtGte), protectgroup.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		pgq.Where(protectgroup.And(protectgroup.UpdatedAtGTE(queryParam.UpdatedAtGte), protectgroup.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	for _, v := range search {

		orPredicate = append(orPredicate, protectgroup.RemarkContainsFold(v))

		orPredicate = append(orPredicate, protectgroup.GroupNameContainsFold(v))

		orPredicate = append(orPredicate, protectgroup.ExpandIPContainsFold(v))

	}
	if len(orPredicate) != 0 {
		pgq.Where(protectgroup.Or(orPredicate...))
	}
	return pgq
}

// QuerySkylineDos 查询 SkylineDos 值
func (sdq *SkylineDosQuery) QueryItemSkylineDos(input *SkylineDos, queryParam *entity.QueryParam, searchCount bool) *SkylineDosQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			sdq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			sdq.Order(Asc(queryParam.Order))
		}
		sdq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}
	var andPredicate []predicate.SkylineDos
	var orPredicate []predicate.SkylineDos

	if input.TenantID != nil {
		andPredicate = append(andPredicate, skylinedos.TenantID(*input.TenantID))
	}

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		sdq.Where(skylinedos.And(skylinedos.CreatedAtGTE(queryParam.CreatedAtGte), skylinedos.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		sdq.Where(skylinedos.And(skylinedos.UpdatedAtGTE(queryParam.UpdatedAtGte), skylinedos.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if input.Remark != nil {
		andPredicate = append(andPredicate, skylinedos.RemarkContainsFold(*input.Remark))
	}

	if !queryParam.StartTimeGte.IsZero() && !queryParam.StartTimeLte.IsZero() {
		sdq.Where(skylinedos.And(skylinedos.StartTimeGTE(queryParam.StartTimeGte), skylinedos.StartTimeLTE(queryParam.StartTimeLte)))
	}

	if !queryParam.EndTimeGte.IsZero() && !queryParam.EndTimeLte.IsZero() {
		sdq.Where(skylinedos.And(skylinedos.EndTimeGTE(queryParam.EndTimeGte), skylinedos.EndTimeLTE(queryParam.EndTimeLte)))
	}

	if input.Region != "" {
		andPredicate = append(andPredicate, skylinedos.RegionContainsFold(input.Region))
	}

	if input.Resource != "" {
		andPredicate = append(andPredicate, skylinedos.ResourceContainsFold(input.Resource))
	}

	if input.ResourceType != "" {
		andPredicate = append(andPredicate, skylinedos.ResourceTypeContainsFold(input.ResourceType))
	}

	if input.VectorTypes != nil {
		for _, i := range *input.VectorTypes {
			j := i
			orPredicate = append(orPredicate,
				func(s *sql.Selector) {
					s.Where(sqljson.StringContains(skylinedos.FieldVectorTypes, j))
				})
		}
	}

	if input.Status != "" {
		andPredicate = append(andPredicate, skylinedos.StatusContainsFold(input.Status))
	}

	if input.AttackID != "" {
		andPredicate = append(andPredicate, skylinedos.AttackIDContainsFold(input.AttackID))
	}

	if input.Project != "" {
		andPredicate = append(andPredicate, skylinedos.ProjectContainsFold(input.Project))
	}

	if input.DurationTime != 0 {
		andPredicate = append(andPredicate, skylinedos.DurationTime(input.DurationTime))
	}

	if len(andPredicate) != 0 {
		sdq.Where(skylinedos.And(andPredicate...))
	}
	if len(orPredicate) != 0 {
		sdq.Where(skylinedos.Or(orPredicate...))
	}
	return sdq
}

// SearchSkylineDos 搜索 SkylineDos 值
func (sdq *SkylineDosQuery) SearchSkylineDos(input *SkylineDos, queryParam *entity.QueryParam, searchCount bool) *SkylineDosQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			sdq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			sdq.Order(Asc(queryParam.Order))
		}
		sdq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}

	var orPredicate []predicate.SkylineDos
	search := queryParam.Search

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		sdq.Where(skylinedos.And(skylinedos.CreatedAtGTE(queryParam.CreatedAtGte), skylinedos.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		sdq.Where(skylinedos.And(skylinedos.UpdatedAtGTE(queryParam.UpdatedAtGte), skylinedos.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if !queryParam.StartTimeGte.IsZero() && !queryParam.StartTimeLte.IsZero() {
		sdq.Where(skylinedos.And(skylinedos.StartTimeGTE(queryParam.StartTimeGte), skylinedos.StartTimeLTE(queryParam.StartTimeLte)))
	}

	if !queryParam.EndTimeGte.IsZero() && !queryParam.EndTimeLte.IsZero() {
		sdq.Where(skylinedos.And(skylinedos.EndTimeGTE(queryParam.EndTimeGte), skylinedos.EndTimeLTE(queryParam.EndTimeLte)))
	}

	for _, v := range search {

		orPredicate = append(orPredicate, skylinedos.RemarkContainsFold(v))

		orPredicate = append(orPredicate, skylinedos.RegionContainsFold(v))

		orPredicate = append(orPredicate, skylinedos.ResourceContainsFold(v))

		orPredicate = append(orPredicate, skylinedos.ResourceTypeContainsFold(v))

		orPredicate = append(orPredicate, skylinedos.StatusContainsFold(v))

		orPredicate = append(orPredicate, skylinedos.AttackIDContainsFold(v))

		orPredicate = append(orPredicate, skylinedos.ProjectContainsFold(v))

	}
	if len(orPredicate) != 0 {
		sdq.Where(skylinedos.Or(orPredicate...))
	}
	return sdq
}

// QuerySocGroupTicket 查询 SocGroupTicket 值
func (sgtq *SocGroupTicketQuery) QueryItemSocGroupTicket(input *SocGroupTicket, queryParam *entity.QueryParam, searchCount bool) *SocGroupTicketQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			sgtq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			sgtq.Order(Asc(queryParam.Order))
		}
		sgtq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}
	var andPredicate []predicate.SocGroupTicket
	var orPredicate []predicate.SocGroupTicket

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		sgtq.Where(socgroupticket.And(socgroupticket.CreatedAtGTE(queryParam.CreatedAtGte), socgroupticket.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		sgtq.Where(socgroupticket.And(socgroupticket.UpdatedAtGTE(queryParam.UpdatedAtGte), socgroupticket.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if input.TenantID != nil {
		andPredicate = append(andPredicate, socgroupticket.TenantID(*input.TenantID))
	}

	if input.Remark != nil {
		andPredicate = append(andPredicate, socgroupticket.RemarkContainsFold(*input.Remark))
	}

	if input.Name != "" {
		andPredicate = append(andPredicate, socgroupticket.NameContainsFold(input.Name))
	}

	if input.Type != "" {
		andPredicate = append(andPredicate, socgroupticket.TypeContainsFold(input.Type))
	}

	if input.Description != "" {
		andPredicate = append(andPredicate, socgroupticket.DescriptionContainsFold(input.Description))
	}

	if input.FollowList != nil {
		for _, i := range *input.FollowList {
			j := i
			orPredicate = append(orPredicate,
				func(s *sql.Selector) {
					s.Where(sqljson.ValueContains(socgroupticket.FieldFollowList, j))
				})
		}
	}

	if input.DepartmentID != 0 {
		andPredicate = append(andPredicate, socgroupticket.DepartmentID(input.DepartmentID))
	}

	if input.IPList != nil {
		for _, i := range *input.IPList {
			j := i
			orPredicate = append(orPredicate,
				func(s *sql.Selector) {
					s.Where(sqljson.StringContains(socgroupticket.FieldIPList, j))
				})
		}
	}

	if input.MinBandwidth != 0 {
		andPredicate = append(andPredicate, socgroupticket.MinBandwidth(input.MinBandwidth))
	}

	if input.DivertType != 0 {
		andPredicate = append(andPredicate, socgroupticket.DivertType(input.DivertType))
	}

	if input.OpType != 0 {
		andPredicate = append(andPredicate, socgroupticket.OpType(input.OpType))
	}

	if !queryParam.OpTimeGte.IsZero() && !queryParam.OpTimeLte.IsZero() {
		sgtq.Where(socgroupticket.And(socgroupticket.OpTimeGTE(queryParam.OpTimeGte), socgroupticket.OpTimeLTE(queryParam.OpTimeLte)))
	}

	if input.ConfigType != 0 {
		andPredicate = append(andPredicate, socgroupticket.ConfigType(input.ConfigType))
	}

	if input.ConfigArgs != "" {
		andPredicate = append(andPredicate, socgroupticket.ConfigArgsContainsFold(input.ConfigArgs))
	}

	if input.ProductName != "" {
		andPredicate = append(andPredicate, socgroupticket.ProductNameContainsFold(input.ProductName))
	}

	if input.ProductCode != "" {
		andPredicate = append(andPredicate, socgroupticket.ProductCodeContainsFold(input.ProductCode))
	}

	if input.GroupTicketID != 0 {
		andPredicate = append(andPredicate, socgroupticket.GroupTicketID(input.GroupTicketID))
	}

	if input.ErrorInfo != "" {
		andPredicate = append(andPredicate, socgroupticket.ErrorInfoContainsFold(input.ErrorInfo))
	}

	if input.CreateUserID != nil {
		andPredicate = append(andPredicate, socgroupticket.CreateUserID(*input.CreateUserID))
	}

	if len(andPredicate) != 0 {
		sgtq.Where(socgroupticket.And(andPredicate...))
	}
	if len(orPredicate) != 0 {
		sgtq.Where(socgroupticket.Or(orPredicate...))
	}
	return sgtq
}

// SearchSocGroupTicket 搜索 SocGroupTicket 值
func (sgtq *SocGroupTicketQuery) SearchSocGroupTicket(input *SocGroupTicket, queryParam *entity.QueryParam, searchCount bool) *SocGroupTicketQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			sgtq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			sgtq.Order(Asc(queryParam.Order))
		}
		sgtq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}

	var orPredicate []predicate.SocGroupTicket
	search := queryParam.Search

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		sgtq.Where(socgroupticket.And(socgroupticket.CreatedAtGTE(queryParam.CreatedAtGte), socgroupticket.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		sgtq.Where(socgroupticket.And(socgroupticket.UpdatedAtGTE(queryParam.UpdatedAtGte), socgroupticket.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if !queryParam.OpTimeGte.IsZero() && !queryParam.OpTimeLte.IsZero() {
		sgtq.Where(socgroupticket.And(socgroupticket.OpTimeGTE(queryParam.OpTimeGte), socgroupticket.OpTimeLTE(queryParam.OpTimeLte)))
	}

	for _, v := range search {

		orPredicate = append(orPredicate, socgroupticket.RemarkContainsFold(v))

		orPredicate = append(orPredicate, socgroupticket.NameContainsFold(v))

		orPredicate = append(orPredicate, socgroupticket.TypeContainsFold(v))

		orPredicate = append(orPredicate, socgroupticket.DescriptionContainsFold(v))

		orPredicate = append(orPredicate, socgroupticket.ConfigArgsContainsFold(v))

		orPredicate = append(orPredicate, socgroupticket.ProductNameContainsFold(v))

		orPredicate = append(orPredicate, socgroupticket.ProductCodeContainsFold(v))

		orPredicate = append(orPredicate, socgroupticket.ErrorInfoContainsFold(v))

	}
	if len(orPredicate) != 0 {
		sgtq.Where(socgroupticket.Or(orPredicate...))
	}
	return sgtq
}

// QuerySpectrumAlert 查询 SpectrumAlert 值
func (saq *SpectrumAlertQuery) QueryItemSpectrumAlert(input *SpectrumAlert, queryParam *entity.QueryParam, searchCount bool) *SpectrumAlertQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			saq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			saq.Order(Asc(queryParam.Order))
		}
		saq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}
	var andPredicate []predicate.SpectrumAlert
	var orPredicate []predicate.SpectrumAlert

	if input.TenantID != nil {
		andPredicate = append(andPredicate, spectrumalert.TenantID(*input.TenantID))
	}

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		saq.Where(spectrumalert.And(spectrumalert.CreatedAtGTE(queryParam.CreatedAtGte), spectrumalert.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		saq.Where(spectrumalert.And(spectrumalert.UpdatedAtGTE(queryParam.UpdatedAtGte), spectrumalert.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if input.Remark != nil {
		andPredicate = append(andPredicate, spectrumalert.RemarkContainsFold(*input.Remark))
	}

	if input.ProtectGroupID != nil {
		andPredicate = append(andPredicate, spectrumalert.ProtectGroupID(*input.ProtectGroupID))
	}

	if input.StrategyID != nil {
		andPredicate = append(andPredicate, spectrumalert.StrategyID(*input.StrategyID))
	}

	if input.WofangID != nil {
		andPredicate = append(andPredicate, spectrumalert.WofangID(*input.WofangID))
	}

	if input.ProtectStatus != nil {
		for _, i := range *input.ProtectStatus {
			j := i
			orPredicate = append(orPredicate,
				func(s *sql.Selector) {
					s.Where(sqljson.ValueContains(spectrumalert.FieldProtectStatus, j))
				})
		}
	}

	if input.IP != "" {
		andPredicate = append(andPredicate, spectrumalert.IPContainsFold(input.IP))
	}

	if !queryParam.StartTimeGte.IsZero() && !queryParam.StartTimeLte.IsZero() {
		saq.Where(spectrumalert.And(spectrumalert.StartTimeGTE(queryParam.StartTimeGte), spectrumalert.StartTimeLTE(queryParam.StartTimeLte)))
	}

	if !queryParam.EndTimeGte.IsZero() && !queryParam.EndTimeLte.IsZero() {
		saq.Where(spectrumalert.And(spectrumalert.EndTimeGTE(queryParam.EndTimeGte), spectrumalert.EndTimeLTE(queryParam.EndTimeLte)))
	}

	if input.AttackType != "" {
		andPredicate = append(andPredicate, spectrumalert.AttackTypeContainsFold(input.AttackType))
	}

	if input.MaxPps != 0 {
		andPredicate = append(andPredicate, spectrumalert.MaxPps(input.MaxPps))
	}

	if input.MaxBps != 0 {
		andPredicate = append(andPredicate, spectrumalert.MaxBps(input.MaxBps))
	}

	if input.IspCode != 0 {
		andPredicate = append(andPredicate, spectrumalert.IspCode(input.IspCode))
	}

	if len(andPredicate) != 0 {
		saq.Where(spectrumalert.And(andPredicate...))
	}
	if len(orPredicate) != 0 {
		saq.Where(spectrumalert.Or(orPredicate...))
	}
	return saq
}

// SearchSpectrumAlert 搜索 SpectrumAlert 值
func (saq *SpectrumAlertQuery) SearchSpectrumAlert(input *SpectrumAlert, queryParam *entity.QueryParam, searchCount bool) *SpectrumAlertQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			saq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			saq.Order(Asc(queryParam.Order))
		}
		saq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}

	var orPredicate []predicate.SpectrumAlert
	search := queryParam.Search

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		saq.Where(spectrumalert.And(spectrumalert.CreatedAtGTE(queryParam.CreatedAtGte), spectrumalert.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		saq.Where(spectrumalert.And(spectrumalert.UpdatedAtGTE(queryParam.UpdatedAtGte), spectrumalert.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if !queryParam.StartTimeGte.IsZero() && !queryParam.StartTimeLte.IsZero() {
		saq.Where(spectrumalert.And(spectrumalert.StartTimeGTE(queryParam.StartTimeGte), spectrumalert.StartTimeLTE(queryParam.StartTimeLte)))
	}

	if !queryParam.EndTimeGte.IsZero() && !queryParam.EndTimeLte.IsZero() {
		saq.Where(spectrumalert.And(spectrumalert.EndTimeGTE(queryParam.EndTimeGte), spectrumalert.EndTimeLTE(queryParam.EndTimeLte)))
	}

	for _, v := range search {

		orPredicate = append(orPredicate, spectrumalert.RemarkContainsFold(v))

		orPredicate = append(orPredicate, spectrumalert.IPContainsFold(v))

		orPredicate = append(orPredicate, spectrumalert.AttackTypeContainsFold(v))

	}
	if len(orPredicate) != 0 {
		saq.Where(spectrumalert.Or(orPredicate...))
	}
	return saq
}

// QuerySpectrumData 查询 SpectrumData 值
func (sdq *SpectrumDataQuery) QueryItemSpectrumData(input *SpectrumData, queryParam *entity.QueryParam, searchCount bool) *SpectrumDataQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			sdq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			sdq.Order(Asc(queryParam.Order))
		}
		sdq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}
	var andPredicate []predicate.SpectrumData
	var orPredicate []predicate.SpectrumData

	if input.TenantID != nil {
		andPredicate = append(andPredicate, spectrumdata.TenantID(*input.TenantID))
	}

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		sdq.Where(spectrumdata.And(spectrumdata.CreatedAtGTE(queryParam.CreatedAtGte), spectrumdata.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if input.SpectrumAlertID != nil {
		andPredicate = append(andPredicate, spectrumdata.SpectrumAlertID(*input.SpectrumAlertID))
	}

	if input.IP != "" {
		andPredicate = append(andPredicate, spectrumdata.IPContainsFold(input.IP))
	}

	if !queryParam.TimeGte.IsZero() && !queryParam.TimeLte.IsZero() {
		sdq.Where(spectrumdata.And(spectrumdata.TimeGTE(queryParam.TimeGte), spectrumdata.TimeLTE(queryParam.TimeLte)))
	}

	if input.MonitorID != 0 {
		andPredicate = append(andPredicate, spectrumdata.MonitorID(input.MonitorID))
	}

	if input.DataType != 0 {
		andPredicate = append(andPredicate, spectrumdata.DataType(input.DataType))
	}

	if input.Bps != 0 {
		andPredicate = append(andPredicate, spectrumdata.Bps(input.Bps))
	}

	if input.Pps != 0 {
		andPredicate = append(andPredicate, spectrumdata.Pps(input.Pps))
	}

	if input.SynBps != 0 {
		andPredicate = append(andPredicate, spectrumdata.SynBps(input.SynBps))
	}

	if input.SynPps != 0 {
		andPredicate = append(andPredicate, spectrumdata.SynPps(input.SynPps))
	}

	if input.AckBps != 0 {
		andPredicate = append(andPredicate, spectrumdata.AckBps(input.AckBps))
	}

	if input.AckPps != 0 {
		andPredicate = append(andPredicate, spectrumdata.AckPps(input.AckPps))
	}

	if input.SynAckBps != 0 {
		andPredicate = append(andPredicate, spectrumdata.SynAckBps(input.SynAckBps))
	}

	if input.SynAckPps != 0 {
		andPredicate = append(andPredicate, spectrumdata.SynAckPps(input.SynAckPps))
	}

	if input.IcmpBps != 0 {
		andPredicate = append(andPredicate, spectrumdata.IcmpBps(input.IcmpBps))
	}

	if input.IcmpPps != 0 {
		andPredicate = append(andPredicate, spectrumdata.IcmpPps(input.IcmpPps))
	}

	if input.SmallPps != 0 {
		andPredicate = append(andPredicate, spectrumdata.SmallPps(input.SmallPps))
	}

	if input.NtpPps != 0 {
		andPredicate = append(andPredicate, spectrumdata.NtpPps(input.NtpPps))
	}

	if input.NtpBps != 0 {
		andPredicate = append(andPredicate, spectrumdata.NtpBps(input.NtpBps))
	}

	if input.DNSQueryPps != 0 {
		andPredicate = append(andPredicate, spectrumdata.DNSQueryPps(input.DNSQueryPps))
	}

	if input.DNSQueryBps != 0 {
		andPredicate = append(andPredicate, spectrumdata.DNSQueryBps(input.DNSQueryBps))
	}

	if input.DNSAnswerPps != 0 {
		andPredicate = append(andPredicate, spectrumdata.DNSAnswerPps(input.DNSAnswerPps))
	}

	if input.DNSAnswerBps != 0 {
		andPredicate = append(andPredicate, spectrumdata.DNSAnswerBps(input.DNSAnswerBps))
	}

	if input.SsdpBps != 0 {
		andPredicate = append(andPredicate, spectrumdata.SsdpBps(input.SsdpBps))
	}

	if input.SsdpPps != 0 {
		andPredicate = append(andPredicate, spectrumdata.SsdpPps(input.SsdpPps))
	}

	if input.UDPPps != 0 {
		andPredicate = append(andPredicate, spectrumdata.UDPPps(input.UDPPps))
	}

	if input.UDPBps != 0 {
		andPredicate = append(andPredicate, spectrumdata.UDPBps(input.UDPBps))
	}

	if input.QPS != 0 {
		andPredicate = append(andPredicate, spectrumdata.QPS(input.QPS))
	}

	if input.ReceiveCount != 0 {
		andPredicate = append(andPredicate, spectrumdata.ReceiveCount(input.ReceiveCount))
	}

	if input.IPType != 0 {
		andPredicate = append(andPredicate, spectrumdata.IPType(input.IPType))
	}

	if input.Monitor != nil {
		andPredicate = append(andPredicate, spectrumdata.MonitorContainsFold(*input.Monitor))
	}

	if input.Product != nil {
		andPredicate = append(andPredicate, spectrumdata.ProductContainsFold(*input.Product))
	}

	if input.Host != nil {
		andPredicate = append(andPredicate, spectrumdata.HostContainsFold(*input.Host))
	}

	if len(andPredicate) != 0 {
		sdq.Where(spectrumdata.And(andPredicate...))
	}
	if len(orPredicate) != 0 {
		sdq.Where(spectrumdata.Or(orPredicate...))
	}
	return sdq
}

// SearchSpectrumData 搜索 SpectrumData 值
func (sdq *SpectrumDataQuery) SearchSpectrumData(input *SpectrumData, queryParam *entity.QueryParam, searchCount bool) *SpectrumDataQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			sdq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			sdq.Order(Asc(queryParam.Order))
		}
		sdq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}

	var orPredicate []predicate.SpectrumData
	search := queryParam.Search

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		sdq.Where(spectrumdata.And(spectrumdata.CreatedAtGTE(queryParam.CreatedAtGte), spectrumdata.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.TimeGte.IsZero() && !queryParam.TimeLte.IsZero() {
		sdq.Where(spectrumdata.And(spectrumdata.TimeGTE(queryParam.TimeGte), spectrumdata.TimeLTE(queryParam.TimeLte)))
	}

	for _, v := range search {

		orPredicate = append(orPredicate, spectrumdata.IPContainsFold(v))

		orPredicate = append(orPredicate, spectrumdata.MonitorContainsFold(v))

		orPredicate = append(orPredicate, spectrumdata.ProductContainsFold(v))

		orPredicate = append(orPredicate, spectrumdata.HostContainsFold(v))

	}
	if len(orPredicate) != 0 {
		sdq.Where(spectrumdata.Or(orPredicate...))
	}
	return sdq
}

// QueryStrategy 查询 Strategy 值
func (sq *StrategyQuery) QueryItemStrategy(input *Strategy, queryParam *entity.QueryParam, searchCount bool) *StrategyQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			sq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			sq.Order(Asc(queryParam.Order))
		}
		sq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}
	var andPredicate []predicate.Strategy
	var orPredicate []predicate.Strategy

	if input.TenantID != nil {
		andPredicate = append(andPredicate, strategy.TenantID(*input.TenantID))
	}

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		sq.Where(strategy.And(strategy.CreatedAtGTE(queryParam.CreatedAtGte), strategy.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		sq.Where(strategy.And(strategy.UpdatedAtGTE(queryParam.UpdatedAtGte), strategy.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if input.Remark != nil {
		andPredicate = append(andPredicate, strategy.RemarkContainsFold(*input.Remark))
	}

	if input.Name != "" {
		andPredicate = append(andPredicate, strategy.NameContainsFold(input.Name))
	}

	if input.Type != 0 {
		andPredicate = append(andPredicate, strategy.Type(input.Type))
	}

	if input.Bps != 0 {
		andPredicate = append(andPredicate, strategy.Bps(input.Bps))
	}

	if input.Pps != 0 {
		andPredicate = append(andPredicate, strategy.Pps(input.Pps))
	}

	if input.BpsCount != 0 {
		andPredicate = append(andPredicate, strategy.BpsCount(input.BpsCount))
	}

	if input.PpsCount != 0 {
		andPredicate = append(andPredicate, strategy.PpsCount(input.PpsCount))
	}

	if input.IspCode != 0 {
		andPredicate = append(andPredicate, strategy.IspCode(input.IspCode))
	}

	if len(andPredicate) != 0 {
		sq.Where(strategy.And(andPredicate...))
	}
	if len(orPredicate) != 0 {
		sq.Where(strategy.Or(orPredicate...))
	}
	return sq
}

// SearchStrategy 搜索 Strategy 值
func (sq *StrategyQuery) SearchStrategy(input *Strategy, queryParam *entity.QueryParam, searchCount bool) *StrategyQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			sq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			sq.Order(Asc(queryParam.Order))
		}
		sq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}

	var orPredicate []predicate.Strategy
	search := queryParam.Search

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		sq.Where(strategy.And(strategy.CreatedAtGTE(queryParam.CreatedAtGte), strategy.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		sq.Where(strategy.And(strategy.UpdatedAtGTE(queryParam.UpdatedAtGte), strategy.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	for _, v := range search {

		orPredicate = append(orPredicate, strategy.RemarkContainsFold(v))

		orPredicate = append(orPredicate, strategy.NameContainsFold(v))

	}
	if len(orPredicate) != 0 {
		sq.Where(strategy.Or(orPredicate...))
	}
	return sq
}

// QuerySystemApi 查询 SystemApi 值
func (saq *SystemApiQuery) QueryItemSystemApi(input *SystemApi, queryParam *entity.QueryParam, searchCount bool) *SystemApiQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			saq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			saq.Order(Asc(queryParam.Order))
		}
		saq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}
	var andPredicate []predicate.SystemApi
	var orPredicate []predicate.SystemApi

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		saq.Where(systemapi.And(systemapi.CreatedAtGTE(queryParam.CreatedAtGte), systemapi.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		saq.Where(systemapi.And(systemapi.UpdatedAtGTE(queryParam.UpdatedAtGte), systemapi.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if input.Remark != nil {
		andPredicate = append(andPredicate, systemapi.RemarkContainsFold(*input.Remark))
	}

	if input.Name != "" {
		andPredicate = append(andPredicate, systemapi.NameContainsFold(input.Name))
	}

	if input.Path != "" {
		andPredicate = append(andPredicate, systemapi.PathContainsFold(input.Path))
	}

	if input.HTTPMethod != "" {
		andPredicate = append(andPredicate, systemapi.HTTPMethodContainsFold(input.HTTPMethod))
	}

	if input.Roles != nil {
		for _, i := range *input.Roles {
			j := i
			orPredicate = append(orPredicate,
				func(s *sql.Selector) {
					s.Where(sqljson.StringContains(systemapi.FieldRoles, j))
				})
		}
	}

	if len(andPredicate) != 0 {
		saq.Where(systemapi.And(andPredicate...))
	}
	if len(orPredicate) != 0 {
		saq.Where(systemapi.Or(orPredicate...))
	}
	return saq
}

// SearchSystemApi 搜索 SystemApi 值
func (saq *SystemApiQuery) SearchSystemApi(input *SystemApi, queryParam *entity.QueryParam, searchCount bool) *SystemApiQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			saq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			saq.Order(Asc(queryParam.Order))
		}
		saq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}

	var orPredicate []predicate.SystemApi
	search := queryParam.Search

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		saq.Where(systemapi.And(systemapi.CreatedAtGTE(queryParam.CreatedAtGte), systemapi.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		saq.Where(systemapi.And(systemapi.UpdatedAtGTE(queryParam.UpdatedAtGte), systemapi.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	for _, v := range search {

		orPredicate = append(orPredicate, systemapi.RemarkContainsFold(v))

		orPredicate = append(orPredicate, systemapi.NameContainsFold(v))

		orPredicate = append(orPredicate, systemapi.PathContainsFold(v))

		orPredicate = append(orPredicate, systemapi.HTTPMethodContainsFold(v))

	}
	if len(orPredicate) != 0 {
		saq.Where(systemapi.Or(orPredicate...))
	}
	return saq
}

// QuerySystemConfig 查询 SystemConfig 值
func (scq *SystemConfigQuery) QueryItemSystemConfig(input *SystemConfig, queryParam *entity.QueryParam, searchCount bool) *SystemConfigQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			scq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			scq.Order(Asc(queryParam.Order))
		}
		scq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}
	var andPredicate []predicate.SystemConfig
	var orPredicate []predicate.SystemConfig

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		scq.Where(systemconfig.And(systemconfig.CreatedAtGTE(queryParam.CreatedAtGte), systemconfig.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		scq.Where(systemconfig.And(systemconfig.UpdatedAtGTE(queryParam.UpdatedAtGte), systemconfig.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if input.Remark != nil {
		andPredicate = append(andPredicate, systemconfig.RemarkContainsFold(*input.Remark))
	}

	if input.WofangTestIP != "" {
		andPredicate = append(andPredicate, systemconfig.WofangTestIPContainsFold(input.WofangTestIP))
	}

	if input.NotifyPhones != nil {
		for _, i := range *input.NotifyPhones {
			j := i
			orPredicate = append(orPredicate,
				func(s *sql.Selector) {
					s.Where(sqljson.StringContains(systemconfig.FieldNotifyPhones, j))
				})
		}
	}

	if input.NotifyEmails != nil {
		for _, i := range *input.NotifyEmails {
			j := i
			orPredicate = append(orPredicate,
				func(s *sql.Selector) {
					s.Where(sqljson.StringContains(systemconfig.FieldNotifyEmails, j))
				})
		}
	}

	if input.NotifyScenes != nil {
		for _, i := range *input.NotifyScenes {
			j := i
			orPredicate = append(orPredicate,
				func(s *sql.Selector) {
					s.Where(sqljson.StringContains(systemconfig.FieldNotifyScenes, j))
				})
		}
	}

	if input.IPWhitelists != nil {
		for _, i := range *input.IPWhitelists {
			j := i
			orPredicate = append(orPredicate,
				func(s *sql.Selector) {
					s.Where(sqljson.StringContains(systemconfig.FieldIPWhitelists, j))
				})
		}
	}

	if len(andPredicate) != 0 {
		scq.Where(systemconfig.And(andPredicate...))
	}
	if len(orPredicate) != 0 {
		scq.Where(systemconfig.Or(orPredicate...))
	}
	return scq
}

// SearchSystemConfig 搜索 SystemConfig 值
func (scq *SystemConfigQuery) SearchSystemConfig(input *SystemConfig, queryParam *entity.QueryParam, searchCount bool) *SystemConfigQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			scq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			scq.Order(Asc(queryParam.Order))
		}
		scq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}

	var orPredicate []predicate.SystemConfig
	search := queryParam.Search

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		scq.Where(systemconfig.And(systemconfig.CreatedAtGTE(queryParam.CreatedAtGte), systemconfig.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		scq.Where(systemconfig.And(systemconfig.UpdatedAtGTE(queryParam.UpdatedAtGte), systemconfig.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	for _, v := range search {

		orPredicate = append(orPredicate, systemconfig.RemarkContainsFold(v))

		orPredicate = append(orPredicate, systemconfig.WofangTestIPContainsFold(v))

	}
	if len(orPredicate) != 0 {
		scq.Where(systemconfig.Or(orPredicate...))
	}
	return scq
}

// QueryTenant 查询 Tenant 值
func (tq *TenantQuery) QueryItemTenant(input *Tenant, queryParam *entity.QueryParam, searchCount bool) *TenantQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			tq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			tq.Order(Asc(queryParam.Order))
		}
		tq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}
	var andPredicate []predicate.Tenant
	var orPredicate []predicate.Tenant

	if input.Name != "" {
		andPredicate = append(andPredicate, tenant.NameContainsFold(input.Name))
	}

	if input.Code != "" {
		andPredicate = append(andPredicate, tenant.CodeContainsFold(input.Code))
	}

	if len(andPredicate) != 0 {
		tq.Where(tenant.And(andPredicate...))
	}
	if len(orPredicate) != 0 {
		tq.Where(tenant.Or(orPredicate...))
	}
	return tq
}

// SearchTenant 搜索 Tenant 值
func (tq *TenantQuery) SearchTenant(input *Tenant, queryParam *entity.QueryParam, searchCount bool) *TenantQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			tq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			tq.Order(Asc(queryParam.Order))
		}
		tq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}

	var orPredicate []predicate.Tenant
	search := queryParam.Search

	for _, v := range search {

		orPredicate = append(orPredicate, tenant.NameContainsFold(v))

		orPredicate = append(orPredicate, tenant.CodeContainsFold(v))

	}
	if len(orPredicate) != 0 {
		tq.Where(tenant.Or(orPredicate...))
	}
	return tq
}

// QueryUser 查询 User 值
func (uq *UserQuery) QueryItemUser(input *User, queryParam *entity.QueryParam, searchCount bool) *UserQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			uq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			uq.Order(Asc(queryParam.Order))
		}
		uq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}
	var andPredicate []predicate.User
	var orPredicate []predicate.User

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		uq.Where(user.And(user.CreatedAtGTE(queryParam.CreatedAtGte), user.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		uq.Where(user.And(user.UpdatedAtGTE(queryParam.UpdatedAtGte), user.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if input.Name != "" {
		andPredicate = append(andPredicate, user.NameContainsFold(input.Name))
	}

	if input.Password != "" {
		andPredicate = append(andPredicate, user.PasswordContainsFold(input.Password))
	}

	if len(andPredicate) != 0 {
		uq.Where(user.And(andPredicate...))
	}
	if len(orPredicate) != 0 {
		uq.Where(user.Or(orPredicate...))
	}
	return uq
}

// SearchUser 搜索 User 值
func (uq *UserQuery) SearchUser(input *User, queryParam *entity.QueryParam, searchCount bool) *UserQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			uq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			uq.Order(Asc(queryParam.Order))
		}
		uq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}

	var orPredicate []predicate.User
	search := queryParam.Search

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		uq.Where(user.And(user.CreatedAtGTE(queryParam.CreatedAtGte), user.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		uq.Where(user.And(user.UpdatedAtGTE(queryParam.UpdatedAtGte), user.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	for _, v := range search {

		orPredicate = append(orPredicate, user.NameContainsFold(v))

		orPredicate = append(orPredicate, user.PasswordContainsFold(v))

	}
	if len(orPredicate) != 0 {
		uq.Where(user.Or(orPredicate...))
	}
	return uq
}

// QueryUserOperationLog 查询 UserOperationLog 值
func (uolq *UserOperationLogQuery) QueryItemUserOperationLog(input *UserOperationLog, queryParam *entity.QueryParam, searchCount bool) *UserOperationLogQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			uolq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			uolq.Order(Asc(queryParam.Order))
		}
		uolq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}
	var andPredicate []predicate.UserOperationLog
	var orPredicate []predicate.UserOperationLog

	if input.Remark != nil {
		andPredicate = append(andPredicate, useroperationlog.RemarkContainsFold(*input.Remark))
	}

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		uolq.Where(useroperationlog.And(useroperationlog.CreatedAtGTE(queryParam.CreatedAtGte), useroperationlog.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		uolq.Where(useroperationlog.And(useroperationlog.UpdatedAtGTE(queryParam.UpdatedAtGte), useroperationlog.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if input.Username != "" {
		andPredicate = append(andPredicate, useroperationlog.UsernameContainsFold(input.Username))
	}

	if input.Method != "" {
		andPredicate = append(andPredicate, useroperationlog.MethodContainsFold(input.Method))
	}

	if input.RequestID != "" {
		andPredicate = append(andPredicate, useroperationlog.RequestIDContainsFold(input.RequestID))
	}

	if input.URI != "" {
		andPredicate = append(andPredicate, useroperationlog.URIContainsFold(input.URI))
	}

	if input.RequestBody != "" {
		andPredicate = append(andPredicate, useroperationlog.RequestBodyContainsFold(input.RequestBody))
	}

	if input.Project != "" {
		andPredicate = append(andPredicate, useroperationlog.ProjectContainsFold(input.Project))
	}

	if len(andPredicate) != 0 {
		uolq.Where(useroperationlog.And(andPredicate...))
	}
	if len(orPredicate) != 0 {
		uolq.Where(useroperationlog.Or(orPredicate...))
	}
	return uolq
}

// SearchUserOperationLog 搜索 UserOperationLog 值
func (uolq *UserOperationLogQuery) SearchUserOperationLog(input *UserOperationLog, queryParam *entity.QueryParam, searchCount bool) *UserOperationLogQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			uolq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			uolq.Order(Asc(queryParam.Order))
		}
		uolq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}

	var orPredicate []predicate.UserOperationLog
	search := queryParam.Search

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		uolq.Where(useroperationlog.And(useroperationlog.CreatedAtGTE(queryParam.CreatedAtGte), useroperationlog.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		uolq.Where(useroperationlog.And(useroperationlog.UpdatedAtGTE(queryParam.UpdatedAtGte), useroperationlog.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	for _, v := range search {

		orPredicate = append(orPredicate, useroperationlog.RemarkContainsFold(v))

		orPredicate = append(orPredicate, useroperationlog.UsernameContainsFold(v))

		orPredicate = append(orPredicate, useroperationlog.MethodContainsFold(v))

		orPredicate = append(orPredicate, useroperationlog.RequestIDContainsFold(v))

		orPredicate = append(orPredicate, useroperationlog.URIContainsFold(v))

		orPredicate = append(orPredicate, useroperationlog.RequestBodyContainsFold(v))

		orPredicate = append(orPredicate, useroperationlog.ProjectContainsFold(v))

	}
	if len(orPredicate) != 0 {
		uolq.Where(useroperationlog.Or(orPredicate...))
	}
	return uolq
}

// QueryWofang 查询 Wofang 值
func (wq *WofangQuery) QueryItemWofang(input *Wofang, queryParam *entity.QueryParam, searchCount bool) *WofangQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			wq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			wq.Order(Asc(queryParam.Order))
		}
		wq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}
	var andPredicate []predicate.Wofang
	var orPredicate []predicate.Wofang

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		wq.Where(wofang.And(wofang.CreatedAtGTE(queryParam.CreatedAtGte), wofang.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		wq.Where(wofang.And(wofang.UpdatedAtGTE(queryParam.UpdatedAtGte), wofang.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if input.TenantID != nil {
		andPredicate = append(andPredicate, wofang.TenantID(*input.TenantID))
	}

	if input.Remark != nil {
		andPredicate = append(andPredicate, wofang.RemarkContainsFold(*input.Remark))
	}

	if input.Name != "" {
		andPredicate = append(andPredicate, wofang.NameContainsFold(input.Name))
	}

	if input.IP != "" {
		andPredicate = append(andPredicate, wofang.IPContainsFold(input.IP))
	}

	if input.Type != "" {
		andPredicate = append(andPredicate, wofang.TypeContainsFold(input.Type))
	}

	if input.UnDragSecond != 0 {
		andPredicate = append(andPredicate, wofang.UnDragSecond(input.UnDragSecond))
	}

	if !queryParam.StartTimeGte.IsZero() && !queryParam.StartTimeLte.IsZero() {
		wq.Where(wofang.And(wofang.StartTimeGTE(queryParam.StartTimeGte), wofang.StartTimeLTE(queryParam.StartTimeLte)))
	}

	if input.ErrorInfo != "" {
		andPredicate = append(andPredicate, wofang.ErrorInfoContainsFold(input.ErrorInfo))
	}

	if input.Status != "" {
		andPredicate = append(andPredicate, wofang.StatusContainsFold(input.Status))
	}

	if input.CreateUserID != nil {
		andPredicate = append(andPredicate, wofang.CreateUserID(*input.CreateUserID))
	}

	if len(andPredicate) != 0 {
		wq.Where(wofang.And(andPredicate...))
	}
	if len(orPredicate) != 0 {
		wq.Where(wofang.Or(orPredicate...))
	}
	return wq
}

// SearchWofang 搜索 Wofang 值
func (wq *WofangQuery) SearchWofang(input *Wofang, queryParam *entity.QueryParam, searchCount bool) *WofangQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			wq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			wq.Order(Asc(queryParam.Order))
		}
		wq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}

	var orPredicate []predicate.Wofang
	search := queryParam.Search

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		wq.Where(wofang.And(wofang.CreatedAtGTE(queryParam.CreatedAtGte), wofang.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		wq.Where(wofang.And(wofang.UpdatedAtGTE(queryParam.UpdatedAtGte), wofang.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if !queryParam.StartTimeGte.IsZero() && !queryParam.StartTimeLte.IsZero() {
		wq.Where(wofang.And(wofang.StartTimeGTE(queryParam.StartTimeGte), wofang.StartTimeLTE(queryParam.StartTimeLte)))
	}

	for _, v := range search {

		orPredicate = append(orPredicate, wofang.RemarkContainsFold(v))

		orPredicate = append(orPredicate, wofang.NameContainsFold(v))

		orPredicate = append(orPredicate, wofang.IPContainsFold(v))

		orPredicate = append(orPredicate, wofang.TypeContainsFold(v))

		orPredicate = append(orPredicate, wofang.ErrorInfoContainsFold(v))

		orPredicate = append(orPredicate, wofang.StatusContainsFold(v))

	}
	if len(orPredicate) != 0 {
		wq.Where(wofang.Or(orPredicate...))
	}
	return wq
}

// QueryWofangAlert 查询 WofangAlert 值
func (waq *WofangAlertQuery) QueryItemWofangAlert(input *WofangAlert, queryParam *entity.QueryParam, searchCount bool) *WofangAlertQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			waq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			waq.Order(Asc(queryParam.Order))
		}
		waq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}
	var andPredicate []predicate.WofangAlert
	var orPredicate []predicate.WofangAlert

	if input.TenantID != nil {
		andPredicate = append(andPredicate, wofangalert.TenantID(*input.TenantID))
	}

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		waq.Where(wofangalert.And(wofangalert.CreatedAtGTE(queryParam.CreatedAtGte), wofangalert.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		waq.Where(wofangalert.And(wofangalert.UpdatedAtGTE(queryParam.UpdatedAtGte), wofangalert.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if input.Remark != nil {
		andPredicate = append(andPredicate, wofangalert.RemarkContainsFold(*input.Remark))
	}

	if input.AttackStatus != 0 {
		andPredicate = append(andPredicate, wofangalert.AttackStatus(input.AttackStatus))
	}

	if input.AttackType != nil {
		for _, i := range *input.AttackType {
			j := i
			orPredicate = append(orPredicate,
				func(s *sql.Selector) {
					s.Where(sqljson.StringContains(wofangalert.FieldAttackType, j))
				})
		}
	}

	if input.DeviceIP != "" {
		andPredicate = append(andPredicate, wofangalert.DeviceIPContainsFold(input.DeviceIP))
	}

	if input.ZoneIP != "" {
		andPredicate = append(andPredicate, wofangalert.ZoneIPContainsFold(input.ZoneIP))
	}

	if input.AttackID != 0 {
		andPredicate = append(andPredicate, wofangalert.AttackID(input.AttackID))
	}

	if !queryParam.StartTimeGte.IsZero() && !queryParam.StartTimeLte.IsZero() {
		waq.Where(wofangalert.And(wofangalert.StartTimeGTE(queryParam.StartTimeGte), wofangalert.StartTimeLTE(queryParam.StartTimeLte)))
	}

	if !queryParam.EndTimeGte.IsZero() && !queryParam.EndTimeLte.IsZero() {
		waq.Where(wofangalert.And(wofangalert.EndTimeGTE(queryParam.EndTimeGte), wofangalert.EndTimeLTE(queryParam.EndTimeLte)))
	}

	if input.MaxDropBps != 0 {
		andPredicate = append(andPredicate, wofangalert.MaxDropBps(input.MaxDropBps))
	}

	if input.MaxInBps != 0 {
		andPredicate = append(andPredicate, wofangalert.MaxInBps(input.MaxInBps))
	}

	if len(andPredicate) != 0 {
		waq.Where(wofangalert.And(andPredicate...))
	}
	if len(orPredicate) != 0 {
		waq.Where(wofangalert.Or(orPredicate...))
	}
	return waq
}

// SearchWofangAlert 搜索 WofangAlert 值
func (waq *WofangAlertQuery) SearchWofangAlert(input *WofangAlert, queryParam *entity.QueryParam, searchCount bool) *WofangAlertQuery {
	if !searchCount {
		//排序
		if strings.HasPrefix(queryParam.Order, "-") {
			//降序
			waq.Order(Desc(strings.ReplaceAll(queryParam.Order, "-", "")))
		} else {
			//升序
			waq.Order(Asc(queryParam.Order))
		}
		waq.Offset(queryParam.Current).Limit(queryParam.PageSize)
	}

	var orPredicate []predicate.WofangAlert
	search := queryParam.Search

	if !queryParam.CreatedAtGte.IsZero() && !queryParam.CreatedAtLte.IsZero() {
		waq.Where(wofangalert.And(wofangalert.CreatedAtGTE(queryParam.CreatedAtGte), wofangalert.CreatedAtLTE(queryParam.CreatedAtLte)))
	}

	if !queryParam.UpdatedAtGte.IsZero() && !queryParam.UpdatedAtLte.IsZero() {
		waq.Where(wofangalert.And(wofangalert.UpdatedAtGTE(queryParam.UpdatedAtGte), wofangalert.UpdatedAtLTE(queryParam.UpdatedAtLte)))
	}

	if !queryParam.StartTimeGte.IsZero() && !queryParam.StartTimeLte.IsZero() {
		waq.Where(wofangalert.And(wofangalert.StartTimeGTE(queryParam.StartTimeGte), wofangalert.StartTimeLTE(queryParam.StartTimeLte)))
	}

	if !queryParam.EndTimeGte.IsZero() && !queryParam.EndTimeLte.IsZero() {
		waq.Where(wofangalert.And(wofangalert.EndTimeGTE(queryParam.EndTimeGte), wofangalert.EndTimeLTE(queryParam.EndTimeLte)))
	}

	for _, v := range search {

		orPredicate = append(orPredicate, wofangalert.RemarkContainsFold(v))

		orPredicate = append(orPredicate, wofangalert.DeviceIPContainsFold(v))

		orPredicate = append(orPredicate, wofangalert.ZoneIPContainsFold(v))

	}
	if len(orPredicate) != 0 {
		waq.Where(wofangalert.Or(orPredicate...))
	}
	return waq
}
