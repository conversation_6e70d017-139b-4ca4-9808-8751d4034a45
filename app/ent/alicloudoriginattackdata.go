// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"meta/app/ent/alicloudoriginattackdata"
	"meta/app/ent/tenant"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// AliCloudOriginAttackData is the model entity for the AliCloudOriginAttackData schema.
type AliCloudOriginAttackData struct {
	config `json:"-" query:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// 租户Id，可选
	TenantID *int `json:"tenant_id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 备注
	Remark *string `json:"remark,omitempty"`
	// IP 所属项目
	Project string `json:"project,omitempty" query:"project,omitempty"`
	// 云类型
	CloudType string `json:"cloud_type,omitempty" query:"cloud_type,omitempty"`
	// 被攻击IP
	IP string `json:"ip,omitempty" query:"ip,omitempty"`
	// 攻击开始时间
	StartTime time.Time `json:"start_time,omitempty"`
	// 结束时间
	EndTime time.Time `json:"end_time,omitempty"`
	// 攻击持续时间，单位秒
	Duration int `json:"duration,omitempty"`
	// 攻击开始时刻的报文数量大小
	Pps int64 `json:"pps,omitempty" query:"pps,omitempty"`
	// 攻击开始时刻的请求流量大小
	Mbps int64 `json:"mbps,omitempty" query:"mbps,omitempty"`
	// 攻击事件的当前状态
	Status string `json:"status,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the AliCloudOriginAttackDataQuery when eager-loading is set.
	Edges        AliCloudOriginAttackDataEdges `json:"edges"`
	selectValues sql.SelectValues
}

// AliCloudOriginAttackDataEdges holds the relations/edges for other nodes in the graph.
type AliCloudOriginAttackDataEdges struct {
	// Tenant holds the value of the tenant edge.
	Tenant *Tenant `json:"tenant,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// TenantOrErr returns the Tenant value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e AliCloudOriginAttackDataEdges) TenantOrErr() (*Tenant, error) {
	if e.loadedTypes[0] {
		if e.Tenant == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: tenant.Label}
		}
		return e.Tenant, nil
	}
	return nil, &NotLoadedError{edge: "tenant"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*AliCloudOriginAttackData) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case alicloudoriginattackdata.FieldID, alicloudoriginattackdata.FieldTenantID, alicloudoriginattackdata.FieldDuration, alicloudoriginattackdata.FieldPps, alicloudoriginattackdata.FieldMbps:
			values[i] = new(sql.NullInt64)
		case alicloudoriginattackdata.FieldRemark, alicloudoriginattackdata.FieldProject, alicloudoriginattackdata.FieldCloudType, alicloudoriginattackdata.FieldIP, alicloudoriginattackdata.FieldStatus:
			values[i] = new(sql.NullString)
		case alicloudoriginattackdata.FieldCreatedAt, alicloudoriginattackdata.FieldUpdatedAt, alicloudoriginattackdata.FieldStartTime, alicloudoriginattackdata.FieldEndTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the AliCloudOriginAttackData fields.
func (acoad *AliCloudOriginAttackData) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case alicloudoriginattackdata.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			acoad.ID = int(value.Int64)
		case alicloudoriginattackdata.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				acoad.TenantID = new(int)
				*acoad.TenantID = int(value.Int64)
			}
		case alicloudoriginattackdata.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				acoad.CreatedAt = value.Time
			}
		case alicloudoriginattackdata.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				acoad.UpdatedAt = value.Time
			}
		case alicloudoriginattackdata.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				acoad.Remark = new(string)
				*acoad.Remark = value.String
			}
		case alicloudoriginattackdata.FieldProject:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field project", values[i])
			} else if value.Valid {
				acoad.Project = value.String
			}
		case alicloudoriginattackdata.FieldCloudType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field cloud_type", values[i])
			} else if value.Valid {
				acoad.CloudType = value.String
			}
		case alicloudoriginattackdata.FieldIP:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field ip", values[i])
			} else if value.Valid {
				acoad.IP = value.String
			}
		case alicloudoriginattackdata.FieldStartTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field start_time", values[i])
			} else if value.Valid {
				acoad.StartTime = value.Time
			}
		case alicloudoriginattackdata.FieldEndTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field end_time", values[i])
			} else if value.Valid {
				acoad.EndTime = value.Time
			}
		case alicloudoriginattackdata.FieldDuration:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field duration", values[i])
			} else if value.Valid {
				acoad.Duration = int(value.Int64)
			}
		case alicloudoriginattackdata.FieldPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field pps", values[i])
			} else if value.Valid {
				acoad.Pps = value.Int64
			}
		case alicloudoriginattackdata.FieldMbps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field mbps", values[i])
			} else if value.Valid {
				acoad.Mbps = value.Int64
			}
		case alicloudoriginattackdata.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				acoad.Status = value.String
			}
		default:
			acoad.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the AliCloudOriginAttackData.
// This includes values selected through modifiers, order, etc.
func (acoad *AliCloudOriginAttackData) Value(name string) (ent.Value, error) {
	return acoad.selectValues.Get(name)
}

// QueryTenant queries the "tenant" edge of the AliCloudOriginAttackData entity.
func (acoad *AliCloudOriginAttackData) QueryTenant() *TenantQuery {
	return NewAliCloudOriginAttackDataClient(acoad.config).QueryTenant(acoad)
}

// Update returns a builder for updating this AliCloudOriginAttackData.
// Note that you need to call AliCloudOriginAttackData.Unwrap() before calling this method if this AliCloudOriginAttackData
// was returned from a transaction, and the transaction was committed or rolled back.
func (acoad *AliCloudOriginAttackData) Update() *AliCloudOriginAttackDataUpdateOne {
	return NewAliCloudOriginAttackDataClient(acoad.config).UpdateOne(acoad)
}

// Unwrap unwraps the AliCloudOriginAttackData entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (acoad *AliCloudOriginAttackData) Unwrap() *AliCloudOriginAttackData {
	_tx, ok := acoad.config.driver.(*txDriver)
	if !ok {
		panic("ent: AliCloudOriginAttackData is not a transactional entity")
	}
	acoad.config.driver = _tx.drv
	return acoad
}

// String implements the fmt.Stringer.
func (acoad *AliCloudOriginAttackData) String() string {
	var builder strings.Builder
	builder.WriteString("AliCloudOriginAttackData(")
	builder.WriteString(fmt.Sprintf("id=%v, ", acoad.ID))
	if v := acoad.TenantID; v != nil {
		builder.WriteString("tenant_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(acoad.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(acoad.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := acoad.Remark; v != nil {
		builder.WriteString("remark=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("project=")
	builder.WriteString(acoad.Project)
	builder.WriteString(", ")
	builder.WriteString("cloud_type=")
	builder.WriteString(acoad.CloudType)
	builder.WriteString(", ")
	builder.WriteString("ip=")
	builder.WriteString(acoad.IP)
	builder.WriteString(", ")
	builder.WriteString("start_time=")
	builder.WriteString(acoad.StartTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("end_time=")
	builder.WriteString(acoad.EndTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("duration=")
	builder.WriteString(fmt.Sprintf("%v", acoad.Duration))
	builder.WriteString(", ")
	builder.WriteString("pps=")
	builder.WriteString(fmt.Sprintf("%v", acoad.Pps))
	builder.WriteString(", ")
	builder.WriteString("mbps=")
	builder.WriteString(fmt.Sprintf("%v", acoad.Mbps))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(acoad.Status)
	builder.WriteByte(')')
	return builder.String()
}

// AliCloudOriginAttackDataSlice is a parsable slice of AliCloudOriginAttackData.
type AliCloudOriginAttackDataSlice []*AliCloudOriginAttackData
