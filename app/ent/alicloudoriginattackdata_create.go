// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/alicloudoriginattackdata"
	"meta/app/ent/tenant"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// AliCloudOriginAttackDataCreate is the builder for creating a AliCloudOriginAttackData entity.
type AliCloudOriginAttackDataCreate struct {
	config
	mutation *AliCloudOriginAttackDataMutation
	hooks    []Hook
}

// SetTenantID sets the "tenant_id" field.
func (acoadc *AliCloudOriginAttackDataCreate) SetTenantID(i int) *AliCloudOriginAttackDataCreate {
	acoadc.mutation.SetTenantID(i)
	return acoadc
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (acoadc *AliCloudOriginAttackDataCreate) SetNillableTenantID(i *int) *AliCloudOriginAttackDataCreate {
	if i != nil {
		acoadc.SetTenantID(*i)
	}
	return acoadc
}

// SetCreatedAt sets the "created_at" field.
func (acoadc *AliCloudOriginAttackDataCreate) SetCreatedAt(t time.Time) *AliCloudOriginAttackDataCreate {
	acoadc.mutation.SetCreatedAt(t)
	return acoadc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (acoadc *AliCloudOriginAttackDataCreate) SetNillableCreatedAt(t *time.Time) *AliCloudOriginAttackDataCreate {
	if t != nil {
		acoadc.SetCreatedAt(*t)
	}
	return acoadc
}

// SetUpdatedAt sets the "updated_at" field.
func (acoadc *AliCloudOriginAttackDataCreate) SetUpdatedAt(t time.Time) *AliCloudOriginAttackDataCreate {
	acoadc.mutation.SetUpdatedAt(t)
	return acoadc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (acoadc *AliCloudOriginAttackDataCreate) SetNillableUpdatedAt(t *time.Time) *AliCloudOriginAttackDataCreate {
	if t != nil {
		acoadc.SetUpdatedAt(*t)
	}
	return acoadc
}

// SetRemark sets the "remark" field.
func (acoadc *AliCloudOriginAttackDataCreate) SetRemark(s string) *AliCloudOriginAttackDataCreate {
	acoadc.mutation.SetRemark(s)
	return acoadc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (acoadc *AliCloudOriginAttackDataCreate) SetNillableRemark(s *string) *AliCloudOriginAttackDataCreate {
	if s != nil {
		acoadc.SetRemark(*s)
	}
	return acoadc
}

// SetProject sets the "project" field.
func (acoadc *AliCloudOriginAttackDataCreate) SetProject(s string) *AliCloudOriginAttackDataCreate {
	acoadc.mutation.SetProject(s)
	return acoadc
}

// SetCloudType sets the "cloud_type" field.
func (acoadc *AliCloudOriginAttackDataCreate) SetCloudType(s string) *AliCloudOriginAttackDataCreate {
	acoadc.mutation.SetCloudType(s)
	return acoadc
}

// SetIP sets the "ip" field.
func (acoadc *AliCloudOriginAttackDataCreate) SetIP(s string) *AliCloudOriginAttackDataCreate {
	acoadc.mutation.SetIP(s)
	return acoadc
}

// SetStartTime sets the "start_time" field.
func (acoadc *AliCloudOriginAttackDataCreate) SetStartTime(t time.Time) *AliCloudOriginAttackDataCreate {
	acoadc.mutation.SetStartTime(t)
	return acoadc
}

// SetEndTime sets the "end_time" field.
func (acoadc *AliCloudOriginAttackDataCreate) SetEndTime(t time.Time) *AliCloudOriginAttackDataCreate {
	acoadc.mutation.SetEndTime(t)
	return acoadc
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (acoadc *AliCloudOriginAttackDataCreate) SetNillableEndTime(t *time.Time) *AliCloudOriginAttackDataCreate {
	if t != nil {
		acoadc.SetEndTime(*t)
	}
	return acoadc
}

// SetDuration sets the "duration" field.
func (acoadc *AliCloudOriginAttackDataCreate) SetDuration(i int) *AliCloudOriginAttackDataCreate {
	acoadc.mutation.SetDuration(i)
	return acoadc
}

// SetNillableDuration sets the "duration" field if the given value is not nil.
func (acoadc *AliCloudOriginAttackDataCreate) SetNillableDuration(i *int) *AliCloudOriginAttackDataCreate {
	if i != nil {
		acoadc.SetDuration(*i)
	}
	return acoadc
}

// SetPps sets the "pps" field.
func (acoadc *AliCloudOriginAttackDataCreate) SetPps(i int64) *AliCloudOriginAttackDataCreate {
	acoadc.mutation.SetPps(i)
	return acoadc
}

// SetMbps sets the "mbps" field.
func (acoadc *AliCloudOriginAttackDataCreate) SetMbps(i int64) *AliCloudOriginAttackDataCreate {
	acoadc.mutation.SetMbps(i)
	return acoadc
}

// SetStatus sets the "status" field.
func (acoadc *AliCloudOriginAttackDataCreate) SetStatus(s string) *AliCloudOriginAttackDataCreate {
	acoadc.mutation.SetStatus(s)
	return acoadc
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (acoadc *AliCloudOriginAttackDataCreate) SetTenant(t *Tenant) *AliCloudOriginAttackDataCreate {
	return acoadc.SetTenantID(t.ID)
}

// Mutation returns the AliCloudOriginAttackDataMutation object of the builder.
func (acoadc *AliCloudOriginAttackDataCreate) Mutation() *AliCloudOriginAttackDataMutation {
	return acoadc.mutation
}

// Save creates the AliCloudOriginAttackData in the database.
func (acoadc *AliCloudOriginAttackDataCreate) Save(ctx context.Context) (*AliCloudOriginAttackData, error) {
	if err := acoadc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, acoadc.sqlSave, acoadc.mutation, acoadc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (acoadc *AliCloudOriginAttackDataCreate) SaveX(ctx context.Context) *AliCloudOriginAttackData {
	v, err := acoadc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (acoadc *AliCloudOriginAttackDataCreate) Exec(ctx context.Context) error {
	_, err := acoadc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (acoadc *AliCloudOriginAttackDataCreate) ExecX(ctx context.Context) {
	if err := acoadc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (acoadc *AliCloudOriginAttackDataCreate) defaults() error {
	if _, ok := acoadc.mutation.CreatedAt(); !ok {
		if alicloudoriginattackdata.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized alicloudoriginattackdata.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := alicloudoriginattackdata.DefaultCreatedAt()
		acoadc.mutation.SetCreatedAt(v)
	}
	if _, ok := acoadc.mutation.UpdatedAt(); !ok {
		if alicloudoriginattackdata.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized alicloudoriginattackdata.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := alicloudoriginattackdata.DefaultUpdatedAt()
		acoadc.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (acoadc *AliCloudOriginAttackDataCreate) check() error {
	if _, ok := acoadc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "AliCloudOriginAttackData.created_at"`)}
	}
	if _, ok := acoadc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "AliCloudOriginAttackData.updated_at"`)}
	}
	if v, ok := acoadc.mutation.Remark(); ok {
		if err := alicloudoriginattackdata.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "AliCloudOriginAttackData.remark": %w`, err)}
		}
	}
	if _, ok := acoadc.mutation.Project(); !ok {
		return &ValidationError{Name: "project", err: errors.New(`ent: missing required field "AliCloudOriginAttackData.project"`)}
	}
	if _, ok := acoadc.mutation.CloudType(); !ok {
		return &ValidationError{Name: "cloud_type", err: errors.New(`ent: missing required field "AliCloudOriginAttackData.cloud_type"`)}
	}
	if _, ok := acoadc.mutation.IP(); !ok {
		return &ValidationError{Name: "ip", err: errors.New(`ent: missing required field "AliCloudOriginAttackData.ip"`)}
	}
	if _, ok := acoadc.mutation.StartTime(); !ok {
		return &ValidationError{Name: "start_time", err: errors.New(`ent: missing required field "AliCloudOriginAttackData.start_time"`)}
	}
	if _, ok := acoadc.mutation.Pps(); !ok {
		return &ValidationError{Name: "pps", err: errors.New(`ent: missing required field "AliCloudOriginAttackData.pps"`)}
	}
	if _, ok := acoadc.mutation.Mbps(); !ok {
		return &ValidationError{Name: "mbps", err: errors.New(`ent: missing required field "AliCloudOriginAttackData.mbps"`)}
	}
	if _, ok := acoadc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "AliCloudOriginAttackData.status"`)}
	}
	return nil
}

func (acoadc *AliCloudOriginAttackDataCreate) sqlSave(ctx context.Context) (*AliCloudOriginAttackData, error) {
	if err := acoadc.check(); err != nil {
		return nil, err
	}
	_node, _spec := acoadc.createSpec()
	if err := sqlgraph.CreateNode(ctx, acoadc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	acoadc.mutation.id = &_node.ID
	acoadc.mutation.done = true
	return _node, nil
}

func (acoadc *AliCloudOriginAttackDataCreate) createSpec() (*AliCloudOriginAttackData, *sqlgraph.CreateSpec) {
	var (
		_node = &AliCloudOriginAttackData{config: acoadc.config}
		_spec = sqlgraph.NewCreateSpec(alicloudoriginattackdata.Table, sqlgraph.NewFieldSpec(alicloudoriginattackdata.FieldID, field.TypeInt))
	)
	if value, ok := acoadc.mutation.CreatedAt(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := acoadc.mutation.UpdatedAt(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := acoadc.mutation.Remark(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := acoadc.mutation.Project(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldProject, field.TypeString, value)
		_node.Project = value
	}
	if value, ok := acoadc.mutation.CloudType(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldCloudType, field.TypeString, value)
		_node.CloudType = value
	}
	if value, ok := acoadc.mutation.IP(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldIP, field.TypeString, value)
		_node.IP = value
	}
	if value, ok := acoadc.mutation.StartTime(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldStartTime, field.TypeTime, value)
		_node.StartTime = value
	}
	if value, ok := acoadc.mutation.EndTime(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldEndTime, field.TypeTime, value)
		_node.EndTime = value
	}
	if value, ok := acoadc.mutation.Duration(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldDuration, field.TypeInt, value)
		_node.Duration = value
	}
	if value, ok := acoadc.mutation.Pps(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldPps, field.TypeInt64, value)
		_node.Pps = value
	}
	if value, ok := acoadc.mutation.Mbps(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldMbps, field.TypeInt64, value)
		_node.Mbps = value
	}
	if value, ok := acoadc.mutation.Status(); ok {
		_spec.SetField(alicloudoriginattackdata.FieldStatus, field.TypeString, value)
		_node.Status = value
	}
	if nodes := acoadc.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   alicloudoriginattackdata.TenantTable,
			Columns: []string{alicloudoriginattackdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.TenantID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// AliCloudOriginAttackDataCreateBulk is the builder for creating many AliCloudOriginAttackData entities in bulk.
type AliCloudOriginAttackDataCreateBulk struct {
	config
	err      error
	builders []*AliCloudOriginAttackDataCreate
}

// Save creates the AliCloudOriginAttackData entities in the database.
func (acoadcb *AliCloudOriginAttackDataCreateBulk) Save(ctx context.Context) ([]*AliCloudOriginAttackData, error) {
	if acoadcb.err != nil {
		return nil, acoadcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(acoadcb.builders))
	nodes := make([]*AliCloudOriginAttackData, len(acoadcb.builders))
	mutators := make([]Mutator, len(acoadcb.builders))
	for i := range acoadcb.builders {
		func(i int, root context.Context) {
			builder := acoadcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*AliCloudOriginAttackDataMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, acoadcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, acoadcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, acoadcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (acoadcb *AliCloudOriginAttackDataCreateBulk) SaveX(ctx context.Context) []*AliCloudOriginAttackData {
	v, err := acoadcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (acoadcb *AliCloudOriginAttackDataCreateBulk) Exec(ctx context.Context) error {
	_, err := acoadcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (acoadcb *AliCloudOriginAttackDataCreateBulk) ExecX(ctx context.Context) {
	if err := acoadcb.Exec(ctx); err != nil {
		panic(err)
	}
}
