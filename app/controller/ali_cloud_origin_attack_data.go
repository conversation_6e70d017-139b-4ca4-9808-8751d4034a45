package controller

import (
	"context"
	"meta/app/ent"
	"meta/app/service"
	"meta/pkg/common"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
)

type AliCloudOriginAttackDataController struct {
	AliCloudOriginAttackDataService *service.AliCloudOriginAttackDataService
	Logger                          *zap.Logger
}

// Query 根据指定字段、时间范围查询或搜索 AliCloudOriginAttackData
//
//	@Description Query 根据指定字段、时间范围查询或搜索 AliCloudOriginAttackData
//	@Summary Query 根据指定字段、时间范围查询或搜索 AliCloudOriginAttackData
//	@Tags AliCloudOriginAttackData
//	@Accept json
//	@Produce json
//	@Param created_at query string false "created_at" Format(date-time)
//
// @Param updated_at query string false "updated_at" Format(date-time)
// @Param remark query string false "remark"
// @Param event_type query string false "event_type"
// @Param project query string false "project"
// @Param ip query string false "ip" Format(ipv4)
// @Param region query string false "region"
// @Param action query string false "action"
// @Param time query string false "time" Format(date-time)
// @Param pps query integer false "pps"
// @Param bps query integer false "bps"
// @Param ddos_type query string false "ddos_type"
// @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.AliCloudOriginAttackData}
//	@Router /api/v1/alicloudoriginattackdata [get]
func (acoadc *AliCloudOriginAttackDataController) Query(c *fiber.Ctx) error {
	acoad := &ent.AliCloudOriginAttackData{}
	qp, err := common.QueryParser(c, acoad)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	count, result, err := acoadc.AliCloudOriginAttackDataService.Query(ctx, acoad, qp)
	return common.NewPageResult(c, err, count, result)
}

// QueryByID 根据 ID 查询 AliCloudOriginAttackData
//
//	@Description QueryByID 根据 ID 查询 AliCloudOriginAttackData
//	@Summary QueryByID 根据 ID 查询 AliCloudOriginAttackData
//	@Tags AliCloudOriginAttackData
//	@Accept json
//	@Produce json
//	@Param id path int true "AliCloudOriginAttackData ID"
//	@Success 200 {object} common.Result{data=ent.AliCloudOriginAttackData}
//	@Router /api/v1/alicloudoriginattackdata/{id} [get]
func (acoadc *AliCloudOriginAttackDataController) QueryByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	result, err := acoadc.AliCloudOriginAttackDataService.QueryByID(ctx, id)
	return common.NewResult(c, err, result)
}

// Create 创建 AliCloudOriginAttackData
//
//	@Description Create 创建 AliCloudOriginAttackData
//	@Summary Create 创建 AliCloudOriginAttackData
//	@Tags AliCloudOriginAttackData
//	@Accept json
//	@Produce json
//	@Param alicloudoriginattackdata body ent.AliCloudOriginAttackData true "AliCloudOriginAttackData"
//	@Success 200 {object} common.Result{data=ent.AliCloudOriginAttackData}
//	@Router /api/v1/alicloudoriginattackdata [post]
func (acoadc *AliCloudOriginAttackDataController) Create(c *fiber.Ctx) error {
	acoad := &ent.AliCloudOriginAttackData{}
	err := common.BodyParser(c, acoad)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	create, err := acoadc.AliCloudOriginAttackDataService.Create(ctx, acoad)
	return common.NewResult(c, err, create)
}

// CreateBulk 批量创建 AliCloudOriginAttackData
//
//	@Description CreateBulk 批量创建 AliCloudOriginAttackData
//	@Summary CreateBulk 批量创建 AliCloudOriginAttackData
//	@Tags AliCloudOriginAttackData
//	@Accept json
//	@Produce json
//	@Param alicloudoriginattackdata body []ent.AliCloudOriginAttackData true "AliCloudOriginAttackData"
//	@Success 200 {object} common.Result{data=[]ent.AliCloudOriginAttackData}
//	@Router /api/v1/alicloudoriginattackdata/bulk [post]
func (acoadc *AliCloudOriginAttackDataController) CreateBulk(c *fiber.Ctx) error {
	acoad := make([]*ent.AliCloudOriginAttackData, 10)
	err := common.RequestBodyParser(c, &acoad)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	bulkData, err := acoadc.AliCloudOriginAttackDataService.CreateBulk(ctx, acoad)
	return common.NewResult(c, err, bulkData)
}

// UpdateByID 根据 ID 修改 AliCloudOriginAttackData
//
//	@Description UpdateByID 根据 ID 修改 AliCloudOriginAttackData
//	@Summary UpdateByID 根据 ID 修改 AliCloudOriginAttackData
//	@Tags AliCloudOriginAttackData
//	@Accept json
//	@Produce json
//	@Param id path int true "AliCloudOriginAttackData ID"
//
// @Param alicloudoriginattackdata body ent.AliCloudOriginAttackData true "AliCloudOriginAttackData"
//
//	@Success 200 {object} common.Result{data=ent.AliCloudOriginAttackData}
//	@Router /api/v1/alicloudoriginattackdata/{id} [put]
func (acoadc *AliCloudOriginAttackDataController) UpdateByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	acoad, err := acoadc.AliCloudOriginAttackDataService.QueryByID(ctx, id)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.BodyParser(c, acoad)
	if err != nil {
		return common.NewResult(c, err)
	}
	data, err := acoadc.AliCloudOriginAttackDataService.UpdateByID(ctx, acoad, id)
	return common.NewResult(c, err, data)
}

// DeleteByID 根据 ID 删除 AliCloudOriginAttackData
//
//	@Description DeleteByID 根据 ID 删除 AliCloudOriginAttackData
//	@Summary DeleteByID 根据 ID 删除 AliCloudOriginAttackData
//	@Tags AliCloudOriginAttackData
//	@Accept json
//	@Produce json
//	@Param id path int true "AliCloudOriginAttackData ID"
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/alicloudoriginattackdata/{id} [delete]
func (acoadc *AliCloudOriginAttackDataController) DeleteByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	err := acoadc.AliCloudOriginAttackDataService.DeleteByID(ctx, id)
	return common.NewResult(c, err)
}

// DeleteBulk 根据 IDs 批量删除 AliCloudOriginAttackData
//
//	@Description DeleteBulk 根据 IDs 批量删除 AliCloudOriginAttackData
//	@Summary DeleteBulk 根据 IDs 批量删除 AliCloudOriginAttackData
//	@Tags AliCloudOriginAttackData
//	@Accept json
//	@Produce json
//	@Param ids body common.DeleteItem true "需要删除的id列表"
//	@Success 200 {object} common.Message
//	@Router /api/v1/alicloudoriginattackdata/bulk/delete [post]
func (acoadc *AliCloudOriginAttackDataController) DeleteBulk(c *fiber.Ctx) error {
	deleteItem := &common.DeleteItem{}
	err := common.RequestBodyParser(c, &deleteItem)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	_, err = acoadc.AliCloudOriginAttackDataService.DeleteBulk(ctx, deleteItem.Ids)
	return common.NewResult(c, err)
}
